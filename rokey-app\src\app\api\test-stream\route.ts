import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  console.log('[Test Stream] Test endpoint called');
  
  // Create a simple SSE stream for testing
  const encoder = new TextEncoder();
  
  const stream = new ReadableStream({
    start(controller) {
      console.log('[Test Stream] Stream started');
      
      // Send initial message
      const message = `data: ${JSON.stringify({ message: 'Test stream connected', timestamp: new Date().toISOString() })}\n\n`;
      controller.enqueue(encoder.encode(message));
      
      // Send periodic messages
      let count = 0;
      const interval = setInterval(() => {
        count++;
        const message = `data: ${JSON.stringify({ message: `Test message ${count}`, timestamp: new Date().toISOString() })}\n\n`;
        controller.enqueue(encoder.encode(message));
        
        if (count >= 5) {
          clearInterval(interval);
          controller.close();
        }
      }, 1000);
    },
    
    cancel() {
      console.log('[Test Stream] Stream cancelled');
    }
  });
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}
