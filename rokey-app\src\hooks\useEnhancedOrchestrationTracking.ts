'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { EnhancedProgressStep } from '@/components/EnhancedOrchestrationProgressCard';

export interface EnhancedOrchestrationEvent {
  type: 'classification' | 'role_selection' | 'workflow_selection' | 'agent_creation' | 
        'supervisor_init' | 'task_planning' | 'agent_working' | 'supervisor_synthesis' | 
        'orchestration_complete' | 'error' | 'thinking' | 'analyzing' | 'connecting' |
        'building' | 'optimizing' | 'validating' | 'finalizing';
  stepId: string;
  title: string;
  description?: string;
  details?: string[];
  metadata?: Record<string, any>;
  timestamp?: Date;
  color?: 'blue' | 'purple' | 'green' | 'orange' | 'pink' | 'indigo' | 'teal' | 'red' | 'yellow' | 'emerald' | 'cyan' | 'rose';
  icon?: 'analysis' | 'roles' | 'workflow' | 'agents' | 'supervisor' | 'planning' | 'working' | 'synthesis' | 'connecting' | 'generating' | 'classification' | 'thinking' | 'building' | 'chatting' | 'writing' | 'coding' | 'designing' | 'learning' | 'optimizing' | 'validating';
  progress?: number;
  duration?: number;
  agent?: string;
}

export function useEnhancedOrchestrationTracking() {
  const [steps, setSteps] = useState<EnhancedProgressStep[]>([]);
  const [isActive, setIsActive] = useState(false);
  const eventQueueRef = useRef<EnhancedOrchestrationEvent[]>([]);
  const processingRef = useRef(false);

  // Process events from the queue with enhanced animations
  const processEventQueue = useCallback(async () => {
    if (processingRef.current || eventQueueRef.current.length === 0) return;
    
    processingRef.current = true;
    
    while (eventQueueRef.current.length > 0) {
      const event = eventQueueRef.current.shift()!;
      
      // Add staggered delay for visual effect
      await new Promise(resolve => setTimeout(resolve, 200));
      
      setSteps(prev => {
        const existingStepIndex = prev.findIndex(step => step.id === event.stepId);
        
        if (existingStepIndex !== -1) {
          // Update existing step
          const updatedSteps = [...prev];
          updatedSteps[existingStepIndex] = {
            ...updatedSteps[existingStepIndex],
            title: event.title,
            description: event.description,
            status: event.type === 'error' ? 'error' : 'in_progress',
            timestamp: event.timestamp || new Date(),
            details: event.details,
            metadata: event.metadata,
            progress: event.progress,
            agent: event.agent
          };
          return updatedSteps;
        } else {
          // Add new step with enhanced properties
          const newStep: EnhancedProgressStep = {
            id: event.stepId,
            title: event.title,
            description: event.description,
            status: event.type === 'error' ? 'error' : 'in_progress',
            timestamp: event.timestamp || new Date(),
            details: event.details,
            metadata: event.metadata,
            icon: event.icon || getIconForEventType(event.type),
            color: event.color || getColorForEventType(event.type),
            progress: event.progress || 0,
            duration: event.duration,
            agent: event.agent
          };
          return [...prev, newStep];
        }
      });
    }
    
    processingRef.current = false;
  }, []);

  // Add event to queue and process
  const trackEvent = useCallback((event: EnhancedOrchestrationEvent) => {
    eventQueueRef.current.push(event);
    setIsActive(true);
    processEventQueue();
  }, [processEventQueue]);

  // Complete a step with enhanced feedback
  const completeStep = useCallback((stepId: string, details?: string[], finalProgress?: number) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { 
            ...step, 
            status: 'completed' as const, 
            details, 
            timestamp: new Date(),
            progress: finalProgress || 100
          }
        : step
    ));
  }, []);

  // Update step progress
  const updateStepProgress = useCallback((stepId: string, progress: number, description?: string) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { 
            ...step, 
            progress,
            ...(description && { description }),
            timestamp: new Date()
          }
        : step
    ));
  }, []);

  // Start orchestration tracking
  const startTracking = useCallback(() => {
    setSteps([]);
    setIsActive(true);
    eventQueueRef.current = [];
  }, []);

  // Complete orchestration tracking
  const completeTracking = useCallback(() => {
    // Mark any in-progress steps as completed
    setSteps(prev => prev.map(step => 
      step.status === 'in_progress' 
        ? { ...step, status: 'completed' as const, progress: 100, timestamp: new Date() }
        : step
    ));
    
    // Keep active for celebration animation
    setTimeout(() => {
      setIsActive(false);
    }, 3000);
  }, []);

  // Reset tracking
  const resetTracking = useCallback(() => {
    setSteps([]);
    setIsActive(false);
    eventQueueRef.current = [];
    processingRef.current = false;
  }, []);

  return {
    steps,
    isActive,
    trackEvent,
    completeStep,
    updateStepProgress,
    startTracking,
    completeTracking,
    resetTracking
  };
}

// Enhanced icon mapping
function getIconForEventType(type: string): EnhancedProgressStep['icon'] {
  const iconMap: Record<string, EnhancedProgressStep['icon']> = {
    classification: 'analysis',
    role_selection: 'roles',
    workflow_selection: 'workflow',
    agent_creation: 'agents',
    supervisor_init: 'supervisor',
    task_planning: 'planning',
    agent_working: 'working',
    supervisor_synthesis: 'synthesis',
    thinking: 'thinking',
    analyzing: 'analysis',
    connecting: 'connecting',
    building: 'building',
    optimizing: 'optimizing',
    validating: 'validating',
    finalizing: 'generating'
  };
  
  return iconMap[type] || 'working';
}

// Enhanced color mapping for variety
function getColorForEventType(type: string): EnhancedProgressStep['color'] {
  const colorMap: Record<string, EnhancedProgressStep['color']> = {
    classification: 'blue',
    role_selection: 'purple',
    workflow_selection: 'indigo',
    agent_creation: 'teal',
    supervisor_init: 'orange',
    task_planning: 'green',
    agent_working: 'cyan',
    supervisor_synthesis: 'emerald',
    thinking: 'purple',
    analyzing: 'blue',
    connecting: 'orange',
    building: 'teal',
    optimizing: 'green',
    validating: 'indigo',
    finalizing: 'rose',
    error: 'red'
  };
  
  return colorMap[type] || 'blue';
}

// Predefined enhanced step templates
export const ENHANCED_ORCHESTRATION_STEPS = {
  // Multi-role orchestration steps with enhanced details
  MULTI_ROLE: {
    CLASSIFICATION: {
      type: 'classification' as const,
      stepId: 'classification',
      title: '🔍 Analyzing Your Request',
      description: 'RouKey AI is examining your request to understand the best approach...',
      color: 'blue' as const,
      icon: 'analysis' as const,
      duration: 3
    },
    ROLE_SELECTION: {
      type: 'role_selection' as const,
      stepId: 'role_selection',
      title: '👥 Selecting AI Specialists',
      description: 'Choosing the perfect team of AI experts for your specific needs...',
      color: 'purple' as const,
      icon: 'roles' as const,
      duration: 2
    },
    WORKFLOW_SELECTION: {
      type: 'workflow_selection' as const,
      stepId: 'workflow_selection',
      title: '⚡ Planning Collaboration Strategy',
      description: 'Designing the optimal workflow for specialist coordination...',
      color: 'indigo' as const,
      icon: 'workflow' as const,
      duration: 2
    },
    AGENT_CREATION: {
      type: 'agent_creation' as const,
      stepId: 'agent_creation',
      title: '🤖 Preparing AI Specialists',
      description: 'Setting up and configuring your dedicated AI team...',
      color: 'teal' as const,
      icon: 'agents' as const,
      duration: 4
    },
    SUPERVISOR_INIT: {
      type: 'supervisor_init' as const,
      stepId: 'supervisor_init',
      title: '✨ Initializing Team Coordinator',
      description: 'Activating the supervisor to manage specialist collaboration...',
      color: 'orange' as const,
      icon: 'supervisor' as const,
      duration: 2
    },
    TASK_PLANNING: {
      type: 'task_planning' as const,
      stepId: 'task_planning',
      title: '📋 Creating Task Plan',
      description: 'Breaking down your request into specialized tasks...',
      color: 'green' as const,
      icon: 'planning' as const,
      duration: 3
    },
    AGENT_WORKING: {
      type: 'agent_working' as const,
      stepId: 'agent_working',
      title: '⚙️ Specialists Working',
      description: 'AI experts are collaborating on your request...',
      color: 'cyan' as const,
      icon: 'working' as const,
      duration: 15
    },
    SUPERVISOR_SYNTHESIS: {
      type: 'supervisor_synthesis' as const,
      stepId: 'supervisor_synthesis',
      title: '🧪 Combining Results',
      description: 'Team coordinator is synthesizing all specialist contributions...',
      color: 'emerald' as const,
      icon: 'synthesis' as const,
      duration: 5
    }
  },

  // Single role steps
  SINGLE_ROLE: {
    THINKING: {
      type: 'thinking' as const,
      stepId: 'thinking',
      title: '🧠 AI Thinking',
      description: 'Processing your request and formulating the best response...',
      color: 'purple' as const,
      icon: 'thinking' as const,
      duration: 5
    },
    GENERATING: {
      type: 'finalizing' as const,
      stepId: 'generating',
      title: '✍️ Generating Response',
      description: 'Creating your personalized response...',
      color: 'rose' as const,
      icon: 'generating' as const,
      duration: 8
    }
  },

  // Additional detailed steps for comprehensive tracking
  DETAILED: {
    CONNECTING: {
      type: 'connecting' as const,
      stepId: 'connecting',
      title: '🔗 Establishing Connections',
      description: 'Setting up secure connections to AI providers...',
      color: 'orange' as const,
      icon: 'connecting' as const,
      duration: 2
    },
    OPTIMIZING: {
      type: 'optimizing' as const,
      stepId: 'optimizing',
      title: '⚡ Optimizing Performance',
      description: 'Fine-tuning system parameters for optimal results...',
      color: 'green' as const,
      icon: 'optimizing' as const,
      duration: 3
    },
    VALIDATING: {
      type: 'validating' as const,
      stepId: 'validating',
      title: '✅ Validating Results',
      description: 'Ensuring quality and accuracy of generated content...',
      color: 'indigo' as const,
      icon: 'validating' as const,
      duration: 2
    }
  }
};
