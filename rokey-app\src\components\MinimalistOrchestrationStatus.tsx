'use client';

import React, { useState, useEffect } from 'react';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  SparklesIcon,
  CogIcon,
  CheckCircleIcon,
  MagnifyingGlassIcon,
  UserGroupIcon,
  CpuChipIcon,
  BeakerIcon,
  BoltIcon,
  FireIcon,
  EyeIcon,
  BrainIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';

export interface MinimalistStatusStep {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  icon?: string;
  color?: string;
  progress?: number;
  details?: string[];
  timestamp?: Date;
}

const STEP_ICONS = {
  analysis: MagnifyingGlassIcon,
  roles: UserGroupIcon,
  workflow: CogIcon,
  agents: CpuChipI<PERSON>,
  supervisor: SparklesIcon,
  planning: BrainIcon,
  working: CogIcon,
  synthesis: BeakerIcon,
  connecting: BoltIcon,
  generating: FireIcon,
  classification: EyeIcon,
  thinking: BrainIcon,
  building: WrenchScrewdriverIcon,
  optimizing: SparklesIcon,
  validating: CheckCircleIcon
};

const STATUS_COLORS = {
  blue: 'from-blue-500 to-blue-600',
  purple: 'from-purple-500 to-purple-600',
  green: 'from-green-500 to-green-600',
  orange: 'from-orange-500 to-orange-600',
  teal: 'from-teal-500 to-teal-600',
  indigo: 'from-indigo-500 to-indigo-600',
  emerald: 'from-emerald-500 to-emerald-600',
  cyan: 'from-cyan-500 to-cyan-600',
  rose: 'from-rose-500 to-rose-600',
  pink: 'from-pink-500 to-pink-600'
};

interface MinimalistOrchestrationStatusProps {
  currentStep?: MinimalistStatusStep;
  isActive: boolean;
  progress?: number;
  className?: string;
  showDetails?: boolean;
}

export default function MinimalistOrchestrationStatus({
  currentStep,
  isActive,
  progress = 0,
  className = '',
  showDetails = true
}: MinimalistOrchestrationStatusProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [displayProgress, setDisplayProgress] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Animate progress changes
  useEffect(() => {
    if (progress !== displayProgress) {
      setIsAnimating(true);
      const timer = setTimeout(() => {
        setDisplayProgress(progress);
        setIsAnimating(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [progress, displayProgress]);

  // Auto-collapse when not active
  useEffect(() => {
    if (!isActive && isExpanded) {
      const timer = setTimeout(() => setIsExpanded(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [isActive, isExpanded]);

  if (!isActive && !currentStep) {
    return null;
  }

  const IconComponent = currentStep?.icon ? STEP_ICONS[currentStep.icon as keyof typeof STEP_ICONS] || CogIcon : CogIcon;
  const colorGradient = STATUS_COLORS[currentStep?.color as keyof typeof STATUS_COLORS] || STATUS_COLORS.blue;

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      {/* Main Status Bar */}
      <div 
        className={`
          relative overflow-hidden rounded-2xl border backdrop-blur-sm transition-all duration-500 ease-out
          ${isActive ? 
            'bg-gradient-to-r from-slate-900/95 to-slate-800/95 border-slate-700/60 shadow-xl' : 
            'bg-gradient-to-r from-slate-900/80 to-slate-800/80 border-slate-700/40 shadow-lg'
          }
          ${isAnimating ? 'scale-[1.01]' : 'scale-100'}
          hover:shadow-2xl hover:border-slate-600/70
        `}
      >
        {/* Animated background glow */}
        {isActive && currentStep?.status === 'in_progress' && (
          <div className="absolute inset-0 opacity-20">
            <div className={`absolute inset-0 bg-gradient-to-r ${colorGradient} animate-pulse`} />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" 
                 style={{ animationDelay: '0.5s', animationDuration: '2s' }} />
          </div>
        )}

        {/* Progress bar background */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-slate-800/50">
          <div 
            className={`h-full bg-gradient-to-r ${colorGradient} transition-all duration-1000 ease-out relative overflow-hidden`}
            style={{ width: `${displayProgress}%` }}
          >
            {/* Shimmer effect on progress bar */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
          </div>
        </div>

        {/* Main content */}
        <div 
          className={`
            flex items-center justify-between p-6 cursor-pointer transition-all duration-300
            ${showDetails && currentStep?.details?.length ? 'hover:bg-white/5' : ''}
          `}
          onClick={() => showDetails && currentStep?.details?.length && setIsExpanded(!isExpanded)}
        >
          {/* Left side - Icon and status */}
          <div className="flex items-center space-x-4">
            {/* Animated status icon */}
            <div className="relative flex-shrink-0">
              {currentStep?.status === 'completed' ? (
                <div className="relative">
                  <CheckCircleIcon className="w-8 h-8 text-green-400 transition-all duration-500" />
                  <div className="absolute inset-0 rounded-full bg-green-400/20 animate-ping" />
                </div>
              ) : currentStep?.status === 'in_progress' ? (
                <div className="relative">
                  <IconComponent className={`w-8 h-8 text-${currentStep.color || 'blue'}-400 transition-all duration-500`} />
                  {/* Multi-layer spinner for active state */}
                  <div className="absolute inset-0 border-2 border-transparent border-t-current rounded-full animate-spin opacity-60" />
                  <div className="absolute inset-1 border border-transparent border-t-current rounded-full animate-spin opacity-40" 
                       style={{ animationDirection: 'reverse', animationDuration: '2s' }} />
                </div>
              ) : currentStep?.status === 'error' ? (
                <div className="w-8 h-8 rounded-full border-2 border-red-400/60 bg-red-500/20 flex items-center justify-center">
                  <div className="w-3 h-3 rounded-full bg-red-400 animate-pulse" />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full border-2 border-slate-600/60 bg-slate-700/20 flex items-center justify-center">
                  <IconComponent className="w-4 h-4 text-slate-400" />
                </div>
              )}
            </div>

            {/* Status text */}
            <div className="min-w-0 flex-1">
              <div className="flex items-center space-x-3">
                <h3 className={`
                  text-lg font-semibold transition-all duration-500
                  ${currentStep?.status === 'completed' ? 'text-green-300' :
                    currentStep?.status === 'in_progress' ? `text-${currentStep.color || 'blue'}-300` :
                    currentStep?.status === 'error' ? 'text-red-300' :
                    'text-slate-400'
                  }
                `}>
                  {currentStep?.title || 'Initializing...'}
                </h3>
                
                {/* Typing indicator for in-progress */}
                {currentStep?.status === 'in_progress' && (
                  <div className="flex space-x-1">
                    <div className={`w-1.5 h-1.5 rounded-full bg-${currentStep.color || 'blue'}-400 animate-pulse`} />
                    <div className={`w-1.5 h-1.5 rounded-full bg-${currentStep.color || 'blue'}-400 animate-pulse`} 
                         style={{ animationDelay: '0.2s' }} />
                    <div className={`w-1.5 h-1.5 rounded-full bg-${currentStep.color || 'blue'}-400 animate-pulse`} 
                         style={{ animationDelay: '0.4s' }} />
                  </div>
                )}
              </div>
              
              {currentStep?.description && (
                <p className={`
                  text-sm mt-1 transition-all duration-500
                  ${currentStep.status === 'completed' ? 'text-green-200/80' :
                    currentStep.status === 'in_progress' ? `text-${currentStep.color || 'blue'}-200/80` :
                    currentStep.status === 'error' ? 'text-red-200/80' :
                    'text-slate-300/80'
                  }
                `}>
                  {currentStep.description}
                </p>
              )}
            </div>
          </div>

          {/* Right side - Progress and expand button */}
          <div className="flex items-center space-x-4 flex-shrink-0">
            {/* Progress percentage */}
            {isActive && (
              <div className="text-right">
                <div className={`
                  text-xl font-bold transition-all duration-500
                  ${currentStep?.status === 'completed' ? 'text-green-300' :
                    currentStep?.status === 'in_progress' ? `text-${currentStep?.color || 'blue'}-300` :
                    'text-slate-400'
                  }
                `}>
                  {Math.round(displayProgress)}%
                </div>
                {currentStep?.timestamp && (
                  <div className="text-xs text-slate-500 mt-1">
                    {currentStep.timestamp.toLocaleTimeString()}
                  </div>
                )}
              </div>
            )}

            {/* Expand/collapse button */}
            {showDetails && currentStep?.details?.length && (
              <button className={`
                p-2 rounded-lg transition-all duration-300
                hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-${currentStep.color || 'blue'}-400/50
              `}>
                {isExpanded ? (
                  <ChevronUpIcon className="w-5 h-5 text-slate-400" />
                ) : (
                  <ChevronDownIcon className="w-5 h-5 text-slate-400" />
                )}
              </button>
            )}
          </div>
        </div>

        {/* Expandable details */}
        {isExpanded && currentStep?.details && (
          <div className="border-t border-slate-700/50 px-6 py-4 bg-slate-900/50">
            <div className="space-y-2">
              {currentStep.details.map((detail, index) => (
                <div 
                  key={index} 
                  className="flex items-start space-x-3 text-sm text-slate-300/90 animate-in slide-in-from-top-2 duration-300"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className={`w-1.5 h-1.5 rounded-full bg-${currentStep.color || 'blue'}-400 mt-2 flex-shrink-0`} />
                  <span>{detail}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Completion celebration effect */}
        {currentStep?.status === 'completed' && isAnimating && (
          <div className="absolute inset-0 rounded-2xl overflow-hidden pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 via-green-400/30 to-green-500/20 animate-pulse" />
          </div>
        )}
      </div>
    </div>
  );
}
