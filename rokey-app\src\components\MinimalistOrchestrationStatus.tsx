'use client';

import React, { useState, useEffect } from 'react';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

export interface MinimalistStatusStep {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  icon?: string;
  color?: string;
  progress?: number;
  details?: string[];
  timestamp?: Date;
}

interface MinimalistOrchestrationStatusProps {
  currentStep?: MinimalistStatusStep;
  isActive: boolean;
  progress?: number;
  className?: string;
  showDetails?: boolean;
  allSteps?: MinimalistStatusStep[];
}

export default function MinimalistOrchestrationStatus({
  currentStep,
  isActive,
  progress = 0,
  className = '',
  showDetails = true,
  allSteps = []
}: MinimalistOrchestrationStatusProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Auto-collapse when not active
  useEffect(() => {
    if (!isActive && isExpanded) {
      const timer = setTimeout(() => setIsExpanded(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [isActive, isExpanded]);

  if (!isActive && !currentStep) {
    return null;
  }

  // Get the main status text
  const getStatusText = () => {
    if (currentStep?.status === 'completed') {
      return 'Thinking complete';
    }
    return currentStep?.title || 'RouKey AI is thinking';
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Gemini-style Status Bar */}
      <div className="flex items-center justify-center">
        <div
          className={`
            flex items-center space-x-3 px-4 py-3 rounded-full transition-all duration-300 cursor-pointer
            ${isActive ?
              'bg-gray-800/90 hover:bg-gray-800' :
              'bg-gray-800/70 hover:bg-gray-800/80'
            }
            backdrop-blur-sm border border-gray-700/50 hover:border-gray-600/70
          `}
          onClick={() => showDetails && setIsExpanded(!isExpanded)}
        >
          {/* Gemini-style diamond spinner */}
          <div className="relative flex items-center justify-center">
            {isActive && currentStep?.status === 'in_progress' ? (
              <div className="relative">
                {/* Main diamond spinner */}
                <div className="w-4 h-4 bg-gradient-to-br from-blue-400 to-purple-500 transform rotate-45 animate-spin"
                     style={{ animationDuration: '2s' }} />
                {/* Inner glow */}
                <div className="absolute inset-0 w-4 h-4 bg-gradient-to-br from-blue-300 to-purple-400 transform rotate-45 animate-pulse opacity-60" />
              </div>
            ) : currentStep?.status === 'completed' ? (
              <div className="w-4 h-4 bg-gradient-to-br from-green-400 to-emerald-500 transform rotate-45" />
            ) : (
              <SparklesIcon className="w-4 h-4 text-gray-400" />
            )}
          </div>

          {/* Status text */}
          <span className="text-sm font-medium text-white">
            {getStatusText()}
          </span>

          {/* Dropdown arrow */}
          {showDetails && (allSteps.length > 0 || currentStep?.details?.length) && (
            <ChevronDownIcon
              className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                isExpanded ? 'rotate-180' : ''
              }`}
            />
          )}
        </div>
      </div>

      {/* Gemini-style Dropdown Details */}
      {isExpanded && (
        <div className="mt-4 mx-auto max-w-2xl">
          <div className="bg-gray-800/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 space-y-4">
            {/* Current step details */}
            {currentStep?.details && currentStep.details.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-3">Current Process</h4>
                <div className="space-y-2">
                  {currentStep.details.map((detail, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-3 text-sm text-gray-400 animate-in slide-in-from-top-2 duration-300"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="w-1.5 h-1.5 rounded-full bg-blue-400 mt-2 flex-shrink-0" />
                      <span>{detail}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* All steps overview */}
            {allSteps.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-3">Process Overview</h4>
                <div className="space-y-3">
                  {allSteps.map((step, index) => (
                    <div
                      key={step.id}
                      className="flex items-center space-x-3 text-sm animate-in slide-in-from-top-2 duration-300"
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      {/* Step status indicator */}
                      <div className="flex-shrink-0">
                        {step.status === 'completed' ? (
                          <div className="w-3 h-3 bg-green-400 rounded-full" />
                        ) : step.status === 'in_progress' ? (
                          <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" />
                        ) : step.status === 'error' ? (
                          <div className="w-3 h-3 bg-red-400 rounded-full" />
                        ) : (
                          <div className="w-3 h-3 bg-gray-600 rounded-full" />
                        )}
                      </div>

                      {/* Step title */}
                      <span className={`
                        ${step.status === 'completed' ? 'text-green-300' :
                          step.status === 'in_progress' ? 'text-blue-300' :
                          step.status === 'error' ? 'text-red-300' :
                          'text-gray-400'
                        }
                      `}>
                        {step.title}
                      </span>

                      {/* Step timestamp */}
                      {step.timestamp && (
                        <span className="text-xs text-gray-500 ml-auto">
                          {step.timestamp.toLocaleTimeString()}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

    </div>
  );
}
