'use client';

import { useState, useCallback, useRef } from 'react';
import { MinimalistStatusStep } from '@/components/MinimalistOrchestrationStatus';

export interface MinimalistStatusEvent {
  type: 'classification' | 'role_selection' | 'workflow_selection' | 'agent_creation' | 
        'supervisor_init' | 'task_planning' | 'agent_working' | 'supervisor_synthesis' | 
        'orchestration_complete' | 'error' | 'thinking' | 'analyzing' | 'connecting' |
        'building' | 'optimizing' | 'validating' | 'finalizing' | 'generating';
  title: string;
  description?: string;
  details?: string[];
  icon?: string;
  color?: string;
  progress?: number;
  duration?: number;
}

export function useMinimalistOrchestrationStatus() {
  const [currentStep, setCurrentStep] = useState<MinimalistStatusStep | null>(null);
  const [allSteps, setAllSteps] = useState<MinimalistStatusStep[]>([]);
  const [isActive, setIsActive] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const progressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const stepCounterRef = useRef(0);

  // Start a new status step
  const startStep = useCallback((event: MinimalistStatusEvent) => {
    stepCounterRef.current += 1;
    const newStep = {
      id: `step_${Date.now()}_${stepCounterRef.current}`,
      title: event.title,
      description: event.description,
      status: 'in_progress' as const,
      icon: event.icon,
      color: event.color,
      progress: event.progress || 0,
      details: event.details,
      timestamp: new Date()
    };

    setCurrentStep(newStep);
    setAllSteps(prev => [...prev, newStep]);
    setIsActive(true);

    // Simulate progress if duration is provided
    if (event.duration && event.duration > 0) {
      simulateProgress(event.duration, event.progress || 0);
    }
  }, []);

  // Update current step
  const updateStep = useCallback((updates: Partial<MinimalistStatusStep>) => {
    try {
      setCurrentStep(prev => {
        if (!prev) return null;
        const updated = { ...prev, ...updates, timestamp: new Date() };

        // Update in allSteps as well
        setAllSteps(prevSteps =>
          prevSteps.map(step => step.id === prev.id ? updated : step)
        );

        return updated;
      });
    } catch (error) {
      console.warn('Error updating step:', error);
    }
  }, []);

  // Complete current step
  const completeStep = useCallback((finalDetails?: string[]) => {
    setCurrentStep(prev => {
      if (!prev) return null;
      const completed = {
        ...prev,
        status: 'completed' as const,
        progress: 100,
        details: finalDetails || prev.details,
        timestamp: new Date()
      };

      // Update in allSteps as well
      setAllSteps(prevSteps =>
        prevSteps.map(step => step.id === prev.id ? completed : step)
      );

      return completed;
    });
    setOverallProgress(100);
  }, []);

  // Error in current step
  const errorStep = useCallback((error: string) => {
    setCurrentStep(prev => prev ? {
      ...prev,
      status: 'error',
      description: error,
      timestamp: new Date()
    } : null);
  }, []);

  // Update progress
  const updateProgress = useCallback((progress: number, description?: string) => {
    setCurrentStep(prev => prev ? {
      ...prev,
      progress,
      ...(description && { description }),
      timestamp: new Date()
    } : null);
    setOverallProgress(progress);
  }, []);

  // Simulate realistic progress over time
  const simulateProgress = useCallback((durationSeconds: number, startProgress: number = 0) => {
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
    }

    const steps = Math.max(10, durationSeconds * 2); // Update every 500ms for smooth animation
    const progressIncrement = (100 - startProgress) / steps;
    let currentProgress = startProgress;
    let stepCount = 0;

    progressTimerRef.current = setInterval(() => {
      stepCount++;
      
      // Use easing function for more natural progress
      const easedProgress = startProgress + (100 - startProgress) * easeOutCubic(stepCount / steps);
      currentProgress = Math.min(easedProgress, 95); // Cap at 95% until manually completed
      
      updateProgress(currentProgress);

      if (stepCount >= steps) {
        if (progressTimerRef.current) {
          clearInterval(progressTimerRef.current);
          progressTimerRef.current = null;
        }
      }
    }, 500);
  }, [updateProgress]);

  // Start orchestration tracking
  const startTracking = useCallback(() => {
    setIsActive(true);
    setOverallProgress(0);
    setCurrentStep(null);
    setAllSteps([]);
    stepCounterRef.current = 0;
  }, []);

  // Complete orchestration tracking
  const completeTracking = useCallback(() => {
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }
    
    setOverallProgress(100);
    
    // Keep active for a bit to show completion state
    setTimeout(() => {
      setIsActive(false);
    }, 3000);
  }, []);

  // Reset tracking
  const resetTracking = useCallback(() => {
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }

    setCurrentStep(null);
    setAllSteps([]);
    setIsActive(false);
    setOverallProgress(0);
    stepCounterRef.current = 0;
  }, []);

  return {
    currentStep,
    allSteps,
    isActive,
    overallProgress,
    startStep,
    updateStep,
    completeStep,
    errorStep,
    updateProgress,
    startTracking,
    completeTracking,
    resetTracking
  };
}

// Easing function for smooth progress animation
function easeOutCubic(t: number): number {
  return 1 - Math.pow(1 - t, 3);
}

// Predefined status steps for common orchestration phases
export const MINIMALIST_STATUS_STEPS = {
  // Connection and initialization
  CONNECTING: {
    type: 'connecting' as const,
    title: '🔗 Establishing Connections',
    description: 'Setting up secure connections to AI providers...',
    icon: 'connecting',
    color: 'orange',
    duration: 2
  },

  // Analysis phase
  ANALYZING: {
    type: 'analyzing' as const,
    title: '🔍 Analyzing Your Request',
    description: 'RouKey AI is examining your request to understand the best approach...',
    icon: 'analysis',
    color: 'blue',
    duration: 3,
    details: [
      'Understanding request complexity and requirements',
      'Identifying the most suitable AI approach',
      'Preparing optimal processing strategy'
    ]
  },

  // Classification
  CLASSIFICATION: {
    type: 'classification' as const,
    title: '🧠 Understanding Your Needs',
    description: 'Determining the perfect AI specialist for your request...',
    icon: 'classification',
    color: 'purple',
    duration: 3,
    details: [
      'Analyzing request type and complexity',
      'Matching requirements to specialist expertise',
      'Selecting optimal AI configuration'
    ]
  },

  // Agent preparation
  PREPARING_SPECIALIST: {
    type: 'agent_creation' as const,
    title: '🤖 Preparing AI Specialist',
    description: 'Setting up the perfect expert for your task...',
    icon: 'agents',
    color: 'teal',
    duration: 4,
    details: [
      'Configuring AI specialist with optimal parameters',
      'Loading relevant knowledge and capabilities',
      'Establishing secure processing environment'
    ]
  },

  // Multi-agent coordination
  COORDINATING_TEAM: {
    type: 'supervisor_init' as const,
    title: '✨ Coordinating AI Team',
    description: 'Setting up collaborative workflow between specialists...',
    icon: 'supervisor',
    color: 'orange',
    duration: 2,
    details: [
      'Initializing team coordination protocols',
      'Establishing communication channels',
      'Preparing collaborative workspace'
    ]
  },

  // Active processing
  PROCESSING: {
    type: 'agent_working' as const,
    title: '⚙️ AI Specialist Working',
    description: 'Your dedicated expert is crafting the perfect response...',
    icon: 'working',
    color: 'cyan',
    duration: 15,
    details: [
      'Applying specialized expertise to your request',
      'Generating high-quality, tailored content',
      'Ensuring accuracy and relevance'
    ]
  },

  // Synthesis phase
  SYNTHESIZING: {
    type: 'supervisor_synthesis' as const,
    title: '🧪 Finalizing Response',
    description: 'Combining all insights into your perfect response...',
    icon: 'synthesis',
    color: 'emerald',
    duration: 5,
    details: [
      'Reviewing and combining all contributions',
      'Ensuring coherence and quality',
      'Applying final optimizations'
    ]
  },

  // Simple thinking for single-agent
  THINKING: {
    type: 'thinking' as const,
    title: '🧠 AI Thinking',
    description: 'Processing your request and formulating the best response...',
    icon: 'thinking',
    color: 'purple',
    duration: 8,
    details: [
      'Analyzing your request in detail',
      'Accessing relevant knowledge and context',
      'Crafting personalized, high-quality response'
    ]
  },

  // Generation phase
  GENERATING: {
    type: 'generating' as const,
    title: '✍️ Generating Response',
    description: 'Creating your personalized response...',
    icon: 'generating',
    color: 'rose',
    duration: 10,
    details: [
      'Applying AI expertise to your specific needs',
      'Generating detailed, accurate content',
      'Ensuring quality and relevance'
    ]
  },

  // Optimization
  OPTIMIZING: {
    type: 'optimizing' as const,
    title: '⚡ Optimizing Results',
    description: 'Fine-tuning for the best possible outcome...',
    icon: 'optimizing',
    color: 'green',
    duration: 3,
    details: [
      'Reviewing generated content for quality',
      'Applying final optimizations',
      'Ensuring perfect alignment with your needs'
    ]
  },

  // Validation
  VALIDATING: {
    type: 'validating' as const,
    title: '✅ Quality Check',
    description: 'Ensuring accuracy and quality of the response...',
    icon: 'validating',
    color: 'indigo',
    duration: 2,
    details: [
      'Performing comprehensive quality review',
      'Validating accuracy and completeness',
      'Confirming optimal response quality'
    ]
  }
};
