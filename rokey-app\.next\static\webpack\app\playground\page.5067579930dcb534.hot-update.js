"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/MinimalistOrchestrationStatus.tsx":
/*!**********************************************************!*\
  !*** ./src/components/MinimalistOrchestrationStatus.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction MinimalistOrchestrationStatus(param) {\n    let { currentStep, isActive, progress = 0, className = '', showDetails = true } = param;\n    var _currentStep_details, _currentStep_details1;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayProgress, setDisplayProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Animate progress changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MinimalistOrchestrationStatus.useEffect\": ()=>{\n            if (progress !== displayProgress) {\n                setIsAnimating(true);\n                const timer = setTimeout({\n                    \"MinimalistOrchestrationStatus.useEffect.timer\": ()=>{\n                        setDisplayProgress(progress);\n                        setIsAnimating(false);\n                    }\n                }[\"MinimalistOrchestrationStatus.useEffect.timer\"], 300);\n                return ({\n                    \"MinimalistOrchestrationStatus.useEffect\": ()=>clearTimeout(timer)\n                })[\"MinimalistOrchestrationStatus.useEffect\"];\n            }\n        }\n    }[\"MinimalistOrchestrationStatus.useEffect\"], [\n        progress,\n        displayProgress\n    ]);\n    // Auto-collapse when not active\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MinimalistOrchestrationStatus.useEffect\": ()=>{\n            if (!isActive && isExpanded) {\n                const timer = setTimeout({\n                    \"MinimalistOrchestrationStatus.useEffect.timer\": ()=>setIsExpanded(false)\n                }[\"MinimalistOrchestrationStatus.useEffect.timer\"], 2000);\n                return ({\n                    \"MinimalistOrchestrationStatus.useEffect\": ()=>clearTimeout(timer)\n                })[\"MinimalistOrchestrationStatus.useEffect\"];\n            }\n        }\n    }[\"MinimalistOrchestrationStatus.useEffect\"], [\n        isActive,\n        isExpanded\n    ]);\n    if (!isActive && !currentStep) {\n        return null;\n    }\n    const IconComponent = (currentStep === null || currentStep === void 0 ? void 0 : currentStep.icon) ? STEP_ICONS[currentStep.icon] || CogIcon : CogIcon;\n    const colorGradient = STATUS_COLORS[currentStep === null || currentStep === void 0 ? void 0 : currentStep.color] || STATUS_COLORS.blue;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-4xl mx-auto \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\\n          relative overflow-hidden rounded-2xl border backdrop-blur-sm transition-all duration-500 ease-out\\n          \".concat(isActive ? 'bg-gradient-to-r from-slate-900/95 to-slate-800/95 border-slate-700/60 shadow-xl' : 'bg-gradient-to-r from-slate-900/80 to-slate-800/80 border-slate-700/40 shadow-lg', \"\\n          \").concat(isAnimating ? 'scale-[1.01]' : 'scale-100', \"\\n          hover:shadow-2xl hover:border-slate-600/70\\n        \"),\n            children: [\n                isActive && (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'in_progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r \".concat(colorGradient, \" animate-pulse\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse\",\n                            style: {\n                                animationDelay: '0.5s',\n                                animationDuration: '2s'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute bottom-0 left-0 right-0 h-1 bg-slate-800/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full bg-gradient-to-r \".concat(colorGradient, \" transition-all duration-1000 ease-out relative overflow-hidden\"),\n                        style: {\n                            width: \"\".concat(displayProgress, \"%\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            flex items-center justify-between p-6 cursor-pointer transition-all duration-300\\n            \".concat(showDetails && (currentStep === null || currentStep === void 0 ? void 0 : (_currentStep_details = currentStep.details) === null || _currentStep_details === void 0 ? void 0 : _currentStep_details.length) ? 'hover:bg-white/5' : '', \"\\n          \"),\n                    onClick: ()=>{\n                        var _currentStep_details;\n                        return showDetails && (currentStep === null || currentStep === void 0 ? void 0 : (_currentStep_details = currentStep.details) === null || _currentStep_details === void 0 ? void 0 : _currentStep_details.length) && setIsExpanded(!isExpanded);\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-shrink-0\",\n                                    children: (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckCircleIcon, {\n                                                className: \"w-8 h-8 text-green-400 transition-all duration-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full bg-green-400/20 animate-ping\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this) : (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'in_progress' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"w-8 h-8 text-\".concat(currentStep.color || 'blue', \"-400 transition-all duration-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 border-2 border-transparent border-t-current rounded-full animate-spin opacity-60\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-1 border border-transparent border-t-current rounded-full animate-spin opacity-40\",\n                                                style: {\n                                                    animationDirection: 'reverse',\n                                                    animationDuration: '2s'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this) : (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'error' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-full border-2 border-red-400/60 bg-red-500/20 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-400 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-full border-2 border-slate-600/60 bg-slate-700/20 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"w-4 h-4 text-slate-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-w-0 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"\\n                  text-lg font-semibold transition-all duration-500\\n                  \".concat((currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'completed' ? 'text-green-300' : (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'in_progress' ? \"text-\".concat(currentStep.color || 'blue', \"-300\") : (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'error' ? 'text-red-300' : 'text-slate-400', \"\\n                \"),\n                                                    children: (currentStep === null || currentStep === void 0 ? void 0 : currentStep.title) || 'Initializing...'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this),\n                                                (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'in_progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1.5 h-1.5 rounded-full bg-\".concat(currentStep.color || 'blue', \"-400 animate-pulse\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1.5 h-1.5 rounded-full bg-\".concat(currentStep.color || 'blue', \"-400 animate-pulse\"),\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1.5 h-1.5 rounded-full bg-\".concat(currentStep.color || 'blue', \"-400 animate-pulse\"),\n                                                            style: {\n                                                                animationDelay: '0.4s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        (currentStep === null || currentStep === void 0 ? void 0 : currentStep.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"\\n                  text-sm mt-1 transition-all duration-500\\n                  \".concat(currentStep.status === 'completed' ? 'text-green-200/80' : currentStep.status === 'in_progress' ? \"text-\".concat(currentStep.color || 'blue', \"-200/80\") : currentStep.status === 'error' ? 'text-red-200/80' : 'text-slate-300/80', \"\\n                \"),\n                                            children: currentStep.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 flex-shrink-0\",\n                            children: [\n                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                  text-xl font-bold transition-all duration-500\\n                  \".concat((currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'completed' ? 'text-green-300' : (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'in_progress' ? \"text-\".concat((currentStep === null || currentStep === void 0 ? void 0 : currentStep.color) || 'blue', \"-300\") : 'text-slate-400', \"\\n                \"),\n                                            children: [\n                                                Math.round(displayProgress),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        (currentStep === null || currentStep === void 0 ? void 0 : currentStep.timestamp) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-slate-500 mt-1\",\n                                            children: currentStep.timestamp.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                showDetails && (currentStep === null || currentStep === void 0 ? void 0 : (_currentStep_details1 = currentStep.details) === null || _currentStep_details1 === void 0 ? void 0 : _currentStep_details1.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"\\n                p-2 rounded-lg transition-all duration-300\\n                hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-\".concat(currentStep.color || 'blue', \"-400/50\\n              \"),\n                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-5 h-5 text-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5 text-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                isExpanded && (currentStep === null || currentStep === void 0 ? void 0 : currentStep.details) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-slate-700/50 px-6 py-4 bg-slate-900/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: currentStep.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3 text-sm text-slate-300/90 animate-in slide-in-from-top-2 duration-300\",\n                                style: {\n                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 rounded-full bg-\".concat(currentStep.color || 'blue', \"-400 mt-2 flex-shrink-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: detail\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this),\n                (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'completed' && isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 rounded-2xl overflow-hidden pointer-events-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-green-500/20 via-green-400/30 to-green-500/20 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(MinimalistOrchestrationStatus, \"m974T5l5KO9uxPrd3Cw2yuxxLhg=\");\n_c = MinimalistOrchestrationStatus;\nvar _c;\n$RefreshReg$(_c, \"MinimalistOrchestrationStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MinimalistOrchestrationStatus.tsx\n"));

/***/ })

});