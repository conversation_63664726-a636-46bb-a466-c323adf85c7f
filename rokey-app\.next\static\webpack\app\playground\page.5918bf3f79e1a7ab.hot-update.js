"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts":
/*!*******************************************************!*\
  !*** ./src/hooks/useMinimalistOrchestrationStatus.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MINIMALIST_STATUS_STEPS: () => (/* binding */ MINIMALIST_STATUS_STEPS),\n/* harmony export */   useMinimalistOrchestrationStatus: () => (/* binding */ useMinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMinimalistOrchestrationStatus,MINIMALIST_STATUS_STEPS auto */ \nfunction useMinimalistOrchestrationStatus() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [allSteps, setAllSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [overallProgress, setOverallProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const progressTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Start a new status step\n    const startStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (event)=>{\n            setCurrentStep({\n                id: \"step_\".concat(Date.now()),\n                title: event.title,\n                description: event.description,\n                status: 'in_progress',\n                icon: event.icon,\n                color: event.color,\n                progress: event.progress || 0,\n                details: event.details,\n                timestamp: new Date()\n            });\n            setIsActive(true);\n            // Simulate progress if duration is provided\n            if (event.duration && event.duration > 0) {\n                simulateProgress(event.duration, event.progress || 0);\n            }\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"], []);\n    // Update current step\n    const updateStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (updates)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prev)=>prev ? {\n                        ...prev,\n                        ...updates,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"], []);\n    // Complete current step\n    const completeStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (finalDetails)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'completed',\n                        progress: 100,\n                        details: finalDetails || prev.details,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n            setOverallProgress(100);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"], []);\n    // Error in current step\n    const errorStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (error)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'error',\n                        description: error,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"], []);\n    // Update progress\n    const updateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (progress, description)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (prev)=>prev ? {\n                        ...prev,\n                        progress,\n                        ...description && {\n                            description\n                        },\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"]);\n            setOverallProgress(progress);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"], []);\n    // Simulate realistic progress over time\n    const simulateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": function(durationSeconds) {\n            let startProgress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n            }\n            const steps = Math.max(10, durationSeconds * 2); // Update every 500ms for smooth animation\n            const progressIncrement = (100 - startProgress) / steps;\n            let currentProgress = startProgress;\n            let stepCount = 0;\n            progressTimerRef.current = setInterval({\n                \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": ()=>{\n                    stepCount++;\n                    // Use easing function for more natural progress\n                    const easedProgress = startProgress + (100 - startProgress) * easeOutCubic(stepCount / steps);\n                    currentProgress = Math.min(easedProgress, 95); // Cap at 95% until manually completed\n                    updateProgress(currentProgress);\n                    if (stepCount >= steps) {\n                        if (progressTimerRef.current) {\n                            clearInterval(progressTimerRef.current);\n                            progressTimerRef.current = null;\n                        }\n                    }\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], 500);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], [\n        updateProgress\n    ]);\n    // Start orchestration tracking\n    const startTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startTracking]\": ()=>{\n            setIsActive(true);\n            setOverallProgress(0);\n            setCurrentStep(null);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startTracking]\"], []);\n    // Complete orchestration tracking\n    const completeTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setOverallProgress(100);\n            // Keep active for a bit to show completion state\n            setTimeout({\n                \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n                    setIsActive(false);\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], 3000);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], []);\n    // Reset tracking\n    const resetTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[resetTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setCurrentStep(null);\n            setIsActive(false);\n            setOverallProgress(0);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[resetTracking]\"], []);\n    return {\n        currentStep,\n        isActive,\n        overallProgress,\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        updateProgress,\n        startTracking,\n        completeTracking,\n        resetTracking\n    };\n}\n// Easing function for smooth progress animation\nfunction easeOutCubic(t) {\n    return 1 - Math.pow(1 - t, 3);\n}\n// Predefined status steps for common orchestration phases\nconst MINIMALIST_STATUS_STEPS = {\n    // Connection and initialization\n    CONNECTING: {\n        type: 'connecting',\n        title: '🔗 Establishing Connections',\n        description: 'Setting up secure connections to AI providers...',\n        icon: 'connecting',\n        color: 'orange',\n        duration: 2\n    },\n    // Analysis phase\n    ANALYZING: {\n        type: 'analyzing',\n        title: '🔍 Analyzing Your Request',\n        description: 'RouKey AI is examining your request to understand the best approach...',\n        icon: 'analysis',\n        color: 'blue',\n        duration: 3,\n        details: [\n            'Understanding request complexity and requirements',\n            'Identifying the most suitable AI approach',\n            'Preparing optimal processing strategy'\n        ]\n    },\n    // Classification\n    CLASSIFICATION: {\n        type: 'classification',\n        title: '🧠 Understanding Your Needs',\n        description: 'Determining the perfect AI specialist for your request...',\n        icon: 'classification',\n        color: 'purple',\n        duration: 3,\n        details: [\n            'Analyzing request type and complexity',\n            'Matching requirements to specialist expertise',\n            'Selecting optimal AI configuration'\n        ]\n    },\n    // Agent preparation\n    PREPARING_SPECIALIST: {\n        type: 'agent_creation',\n        title: '🤖 Preparing AI Specialist',\n        description: 'Setting up the perfect expert for your task...',\n        icon: 'agents',\n        color: 'teal',\n        duration: 4,\n        details: [\n            'Configuring AI specialist with optimal parameters',\n            'Loading relevant knowledge and capabilities',\n            'Establishing secure processing environment'\n        ]\n    },\n    // Multi-agent coordination\n    COORDINATING_TEAM: {\n        type: 'supervisor_init',\n        title: '✨ Coordinating AI Team',\n        description: 'Setting up collaborative workflow between specialists...',\n        icon: 'supervisor',\n        color: 'orange',\n        duration: 2,\n        details: [\n            'Initializing team coordination protocols',\n            'Establishing communication channels',\n            'Preparing collaborative workspace'\n        ]\n    },\n    // Active processing\n    PROCESSING: {\n        type: 'agent_working',\n        title: '⚙️ AI Specialist Working',\n        description: 'Your dedicated expert is crafting the perfect response...',\n        icon: 'working',\n        color: 'cyan',\n        duration: 15,\n        details: [\n            'Applying specialized expertise to your request',\n            'Generating high-quality, tailored content',\n            'Ensuring accuracy and relevance'\n        ]\n    },\n    // Synthesis phase\n    SYNTHESIZING: {\n        type: 'supervisor_synthesis',\n        title: '🧪 Finalizing Response',\n        description: 'Combining all insights into your perfect response...',\n        icon: 'synthesis',\n        color: 'emerald',\n        duration: 5,\n        details: [\n            'Reviewing and combining all contributions',\n            'Ensuring coherence and quality',\n            'Applying final optimizations'\n        ]\n    },\n    // Simple thinking for single-agent\n    THINKING: {\n        type: 'thinking',\n        title: '🧠 AI Thinking',\n        description: 'Processing your request and formulating the best response...',\n        icon: 'thinking',\n        color: 'purple',\n        duration: 8,\n        details: [\n            'Analyzing your request in detail',\n            'Accessing relevant knowledge and context',\n            'Crafting personalized, high-quality response'\n        ]\n    },\n    // Generation phase\n    GENERATING: {\n        type: 'generating',\n        title: '✍️ Generating Response',\n        description: 'Creating your personalized response...',\n        icon: 'generating',\n        color: 'rose',\n        duration: 10,\n        details: [\n            'Applying AI expertise to your specific needs',\n            'Generating detailed, accurate content',\n            'Ensuring quality and relevance'\n        ]\n    },\n    // Optimization\n    OPTIMIZING: {\n        type: 'optimizing',\n        title: '⚡ Optimizing Results',\n        description: 'Fine-tuning for the best possible outcome...',\n        icon: 'optimizing',\n        color: 'green',\n        duration: 3,\n        details: [\n            'Reviewing generated content for quality',\n            'Applying final optimizations',\n            'Ensuring perfect alignment with your needs'\n        ]\n    },\n    // Validation\n    VALIDATING: {\n        type: 'validating',\n        title: '✅ Quality Check',\n        description: 'Ensuring accuracy and quality of the response...',\n        icon: 'validating',\n        color: 'indigo',\n        duration: 2,\n        details: [\n            'Performing comprehensive quality review',\n            'Validating accuracy and completeness',\n            'Confirming optimal response quality'\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\n"));

/***/ })

});