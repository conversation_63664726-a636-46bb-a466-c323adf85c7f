/**
 * Rou<PERSON>eyLLM - Custom LangChain LLM that integrates with RouKey's BYOK system
 * 
 * This class bridges LangGraph.js with RouKey's existing executeProviderRequest
 * function, ensuring all API calls use the user's own API keys.
 */

import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { BaseMessage, AIMessage, HumanMessage, SystemMessage } from '@langchain/core/messages';
import { ChatResult, ChatGeneration } from '@langchain/core/outputs';
import { CallbackManagerForLLMRun } from '@langchain/core/callbacks/manager';
import { decrypt } from '@/lib/encryption';
import { type ApiKey } from '@/types/apiKeys';

// Import the executeProviderRequest function and types
import { executeProviderRequest, type ProviderCallResult, RoKeyChatCompletionRequestSchema } from '@/lib/providers/executeProviderRequest';

interface RouKeyLLMConfig {
  apiKey: ApiKey;
  temperature?: number;
  maxTokens?: number;
  streaming?: boolean;
  customApiConfigId?: string; // Required for multi-role orchestration
}

export class Rou<PERSON>eyLLM extends BaseChatModel {
  private apiKey: ApiKey;
  private temperature: number;
  private maxTokens: number;
  private streaming: boolean;
  private customApiConfigId?: string;

  constructor(config: RouKeyLLMConfig) {
    super({});
    this.apiKey = config.apiKey;
    this.temperature = config.temperature ?? 0.7;
    this.maxTokens = config.maxTokens ?? 2000;
    this.streaming = config.streaming ?? false;
    this.customApiConfigId = config.customApiConfigId;
  }

  _llmType(): string {
    return 'rokey-llm';
  }

  /**
   * Convert LangChain messages to RouKey format
   */
  private convertMessagesToRouKeyFormat(messages: BaseMessage[]): any[] {
    return messages.map(message => {
      if (message instanceof HumanMessage) {
        return { role: 'user', content: message.content };
      } else if (message instanceof AIMessage) {
        return { role: 'assistant', content: message.content };
      } else if (message instanceof SystemMessage) {
        return { role: 'system', content: message.content };
      } else {
        // Fallback for other message types
        return { role: 'user', content: message.content };
      }
    });
  }

  /**
   * Main method that handles LLM calls through RouKey's BYOK system
   */
  async _generate(
    messages: BaseMessage[],
    options?: any,
    runManager?: CallbackManagerForLLMRun
  ): Promise<ChatResult> {
    try {
      console.log(`[RouKey LLM] Making request with ${this.apiKey.provider} (${this.apiKey.label})`);
      
      // Convert messages to RouKey format
      const routeKeyMessages = this.convertMessagesToRouKeyFormat(messages);
      
      // Prepare request payload in RouKey format
      const requestPayload = {
        custom_api_config_id: this.customApiConfigId || 'unknown', // Required by executeProviderRequest
        messages: routeKeyMessages,
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        stream: this.streaming,
        role: 'orchestration', // Mark this as orchestration context for timeout handling
        // Add any additional parameters from options
        ...options
      };

      // Debug logging for multi-role orchestration
      console.log(`[RouKey LLM] Making request with ${this.apiKey.provider} (${this.apiKey.predefined_model_id})`);
      if (!this.customApiConfigId) {
        console.warn(`[RouKey LLM] ⚠️ customApiConfigId is missing! Using 'unknown' - this may cause provider failures`);
      }

      // Decrypt the API key
      const decryptedApiKey = decrypt(this.apiKey.encrypted_api_key);

      // Call RouKey's executeProviderRequest function
      const result = await executeProviderRequest(
        this.apiKey.provider,
        this.apiKey.predefined_model_id,
        decryptedApiKey,
        requestPayload
      );

      if (!result.success || !result.responseData) {
        const errorMessage = result.error?.message || result.error || 'Unknown error';
        const errorType = result.error?.type || 'Unknown';
        console.error(`[RouKey LLM] Provider call failed:`, {
          provider: this.apiKey.provider,
          model: this.apiKey.predefined_model_id,
          status: result.status,
          errorType,
          errorMessage
        });
        throw new Error(`RouKey LLM call failed: ${errorMessage} (${errorType})`);
      }

      // Extract response content
      const content = result.responseData.choices?.[0]?.message?.content || '';
      const finishReason = result.responseData.choices?.[0]?.finish_reason || 'stop';

      // Create LangChain response format
      const generation: ChatGeneration = {
        message: new AIMessage(content),
        text: content,
        generationInfo: {
          finishReason,
          usage: result.responseData.usage,
          provider: this.apiKey.provider,
          model: this.apiKey.predefined_model_id
        }
      };

      console.log(`[RouKey LLM] ✅ Success with ${this.apiKey.provider} - ${content.length} chars`);

      return {
        generations: [generation],
        llmOutput: {
          tokenUsage: result.responseData.usage,
          provider: this.apiKey.provider,
          model: this.apiKey.predefined_model_id
        }
      };

    } catch (error) {
      console.error(`[RouKey LLM] ❌ Error with ${this.apiKey.provider}:`, error);
      throw new Error(`RouKey LLM failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Streaming support for real-time responses
   */
  async *_streamResponseChunks(
    messages: BaseMessage[],
    options?: any,
    runManager?: CallbackManagerForLLMRun
  ): AsyncGenerator<any> {
    // For now, fall back to non-streaming and yield the complete response
    // TODO: Implement true streaming when RouKey's executeProviderRequest supports it
    const result = await this._generate(messages, options, runManager);
    
    if (result.generations.length > 0) {
      yield {
        chunk: result.generations[0].message,
        generationInfo: result.generations[0].generationInfo
      };
    }
  }

  /**
   * Get the identifying parameters for this LLM
   */
  get identifyingParams(): Record<string, any> {
    return {
      provider: this.apiKey.provider,
      model: this.apiKey.predefined_model_id,
      temperature: this.temperature,
      maxTokens: this.maxTokens,
      apiKeyId: this.apiKey.id
    };
  }

  /**
   * Create a new instance with different parameters
   */
  withParameters(config: Partial<RouKeyLLMConfig>): RouKeyLLM {
    return new RouKeyLLM({
      apiKey: config.apiKey ?? this.apiKey,
      temperature: config.temperature ?? this.temperature,
      maxTokens: config.maxTokens ?? this.maxTokens,
      streaming: config.streaming ?? this.streaming,
      customApiConfigId: config.customApiConfigId ?? this.customApiConfigId
    });
  }

  /**
   * Get model information for logging
   */
  getModelInfo(): string {
    return `${this.apiKey.provider}/${this.apiKey.predefined_model_id} (${this.apiKey.label})`;
  }

  /**
   * Bind tools to the LLM (required by LangGraph)
   * For now, we'll return a new instance that can handle tools
   * TODO: Implement actual tool binding when RouKey supports function calling
   */
  bindTools(tools: any[]): RouKeyLLM {
    console.log(`[RouKey LLM] bindTools called with ${tools.length} tools - returning self for now`);
    // For now, just return self since we don't have tool support yet
    // This satisfies LangGraph's requirement without breaking functionality
    return this;
  }

  /**
   * Invoke method (alternative interface for LangGraph compatibility)
   */
  async invoke(input: any, options?: any): Promise<any> {
    // Convert input to messages format if needed
    let messages: BaseMessage[];

    if (Array.isArray(input)) {
      messages = input;
    } else if (input.messages) {
      messages = input.messages;
    } else if (typeof input === 'string') {
      messages = [new HumanMessage(input)];
    } else {
      throw new Error('Invalid input format for RouKeyLLM.invoke()');
    }

    const result = await this._generate(messages, options);
    return result.generations[0]?.message || new AIMessage('');
  }
}

export default RouKeyLLM;
