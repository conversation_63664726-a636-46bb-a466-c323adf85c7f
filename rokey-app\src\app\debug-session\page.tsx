'use client';

import { useState, useEffect } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';

export default function DebugSessionPage() {
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [hybridTestResult, setHybridTestResult] = useState<any>(null);
  const [hybridTesting, setHybridTesting] = useState(false);
  const supabase = createSupabaseBrowserClient();

  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        setSessionInfo({
          hasSession: !!session,
          sessionError: error?.message,
          userId: session?.user?.id,
          userEmail: session?.user?.email,
          userMetadata: session?.user?.user_metadata,
          accessToken: session?.access_token ? 'Present' : 'Missing',
          refreshToken: session?.refresh_token ? 'Present' : 'Missing',
          expiresAt: session?.expires_at,
          timestamp: new Date().toISOString()
        });
      } catch (err) {
        setSessionInfo({
          error: err instanceof Error ? err.message : String(err),
          timestamp: new Date().toISOString()
        });
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state change:', event, !!session);
        checkSession();
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const testSignIn = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>', // Use the test email
        password: 'test123456' // You'll need to provide the correct password
      });

      if (error) {
        alert('Sign in error: ' + error.message);
      } else {
        alert('Sign in successful!');
      }
    } catch (err) {
      alert('Sign in error: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  const testSignOut = async () => {
    try {
      setLoading(true);
      await supabase.auth.signOut();
      alert('Signed out successfully!');
    } catch (err) {
      alert('Sign out error: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  const testHybridOrchestration = async () => {
    try {
      setHybridTesting(true);
      setHybridTestResult(null);

      // Get current session for user ID
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id) {
        alert('Please sign in first to test hybrid orchestration');
        return;
      }

      // Get user's config ID (you may need to adjust this based on your data structure)
      const { data: configs } = await supabase
        .from('custom_api_configs')
        .select('id')
        .eq('user_id', session.user.id)
        .limit(1);

      if (!configs || configs.length === 0) {
        alert('No API configuration found. Please set up your API keys first.');
        return;
      }

      const configId = configs[0].id;

      // Test hybrid orchestration with a complex prompt
      const testPrompt = "I need to brainstorm creative ideas for a new web application and then code the backend API for it. Please help me with both the creative ideation and the technical implementation.";

      console.log('Testing hybrid orchestration with:', {
        prompt: testPrompt,
        configId,
        userId: session.user.id
      });

      const response = await fetch('/api/hybrid-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: testPrompt,
          configId,
          userId: session.user.id
        })
      });

      if (response.ok) {
        if (response.headers.get('content-type')?.includes('text/event-stream')) {
          // Handle streaming response
          const reader = response.body?.getReader();
          const decoder = new TextDecoder();
          let streamResult = '';

          if (reader) {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value);
              streamResult += chunk;

              // Update result in real-time
              setHybridTestResult({
                type: 'streaming',
                content: streamResult,
                status: 'in_progress'
              });
            }
          }

          setHybridTestResult({
            type: 'streaming',
            content: streamResult,
            status: 'completed'
          });
        } else {
          // Handle JSON response
          const result = await response.json();
          setHybridTestResult({
            type: 'json',
            content: result,
            status: 'completed'
          });
        }
      } else {
        const error = await response.json();
        setHybridTestResult({
          type: 'error',
          content: error,
          status: 'failed'
        });
      }

    } catch (err) {
      console.error('Hybrid orchestration test error:', err);
      setHybridTestResult({
        type: 'error',
        content: { error: err instanceof Error ? err.message : String(err) },
        status: 'failed'
      });
    } finally {
      setHybridTesting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p>Loading session info...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Session Debug Page</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Session Info</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(sessionInfo, null, 2)}
          </pre>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
          <div className="space-x-4">
            <button
              onClick={testSignIn}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              disabled={loading}
            >
              Test Sign In
            </button>
            <button
              onClick={testSignOut}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              disabled={loading}
            >
              Test Sign Out
            </button>
            <button
              onClick={() => window.location.reload()}
              className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Refresh Page
            </button>
          </div>
        </div>

        {/* 🚀 REVOLUTIONARY HYBRID ORCHESTRATION TEST SECTION 🚀 */}
        <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border-2 border-orange-200 rounded-lg shadow-lg p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4 text-orange-800">
            🚀 Revolutionary Hybrid CrewAI + AutoGen Orchestration Test
          </h2>
          <p className="text-orange-700 mb-4">
            Test the new hybrid orchestration system that combines CrewAI and Microsoft AutoGen
            for superior multi-role AI coordination with dynamic expert consultation.
          </p>

          <div className="mb-4">
            <button
              onClick={testHybridOrchestration}
              className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-lg hover:from-orange-600 hover:to-red-600 font-semibold shadow-lg transform hover:scale-105 transition-all duration-200"
              disabled={hybridTesting || loading}
            >
              {hybridTesting ? (
                <>
                  <div className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Testing Hybrid Orchestration...
                </>
              ) : (
                '🎯 Test Hybrid Orchestration'
              )}
            </button>
          </div>

          {hybridTestResult && (
            <div className="bg-white rounded-lg p-4 border border-orange-200">
              <h3 className="font-semibold mb-2 text-orange-800">
                Hybrid Orchestration Result ({hybridTestResult.status}):
              </h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {hybridTestResult.type === 'streaming'
                  ? hybridTestResult.content
                  : JSON.stringify(hybridTestResult.content, null, 2)
                }
              </pre>
            </div>
          )}

          <div className="mt-4 bg-orange-100 border border-orange-300 rounded-lg p-3">
            <h4 className="font-semibold text-orange-800 mb-2">🎯 What This Tests:</h4>
            <ul className="list-disc list-inside text-orange-700 space-y-1 text-sm">
              <li><strong>Multi-Role Detection:</strong> Analyzes complex prompts requiring multiple AI specialists</li>
              <li><strong>Dynamic Expert Consultation:</strong> Contacts additional API keys when expertise is needed</li>
              <li><strong>CrewAI + AutoGen Hybrid:</strong> Combines sequential and conversational orchestration</li>
              <li><strong>Smart Role Matching:</strong> Only uses API keys with relevant assigned roles</li>
              <li><strong>Streaming Responses:</strong> Real-time updates during orchestration execution</li>
            </ul>
          </div>
        </div>

        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">Instructions:</h3>
          <ol className="list-decimal list-inside text-yellow-700 space-y-1">
            <li>Check the current session info above</li>
            <li>Try the "Test Sign In" button (update the email/password in the code if needed)</li>
            <li>Check if session info updates after sign in</li>
            <li>Try navigating to /checkout after successful sign in</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
