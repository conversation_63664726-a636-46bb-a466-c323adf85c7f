"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts":
/*!*******************************************************!*\
  !*** ./src/hooks/useMinimalistOrchestrationStatus.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MINIMALIST_STATUS_STEPS: () => (/* binding */ MINIMALIST_STATUS_STEPS),\n/* harmony export */   useMinimalistOrchestrationStatus: () => (/* binding */ useMinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMinimalistOrchestrationStatus,MINIMALIST_STATUS_STEPS auto */ \nfunction useMinimalistOrchestrationStatus() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [allSteps, setAllSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [overallProgress, setOverallProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const progressTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Start a new status step\n    const startStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (event)=>{\n            const newStep = {\n                id: \"step_\".concat(Date.now()),\n                title: event.title,\n                description: event.description,\n                status: 'in_progress',\n                icon: event.icon,\n                color: event.color,\n                progress: event.progress || 0,\n                details: event.details,\n                timestamp: new Date()\n            };\n            setCurrentStep(newStep);\n            setAllSteps({\n                \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (prev)=>[\n                        ...prev,\n                        newStep\n                    ]\n            }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"]);\n            setIsActive(true);\n            // Simulate progress if duration is provided\n            if (event.duration && event.duration > 0) {\n                simulateProgress(event.duration, event.progress || 0);\n            }\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"], []);\n    // Update current step\n    const updateStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (updates)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const updated = {\n                        ...prev,\n                        ...updates,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (step)=>step.id === prev.id ? updated : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n                    return updated;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"], []);\n    // Complete current step\n    const completeStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (finalDetails)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'completed',\n                        progress: 100,\n                        details: finalDetails || prev.details,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n            setOverallProgress(100);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"], []);\n    // Error in current step\n    const errorStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (error)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'error',\n                        description: error,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"], []);\n    // Update progress\n    const updateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (progress, description)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (prev)=>prev ? {\n                        ...prev,\n                        progress,\n                        ...description && {\n                            description\n                        },\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"]);\n            setOverallProgress(progress);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"], []);\n    // Simulate realistic progress over time\n    const simulateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": function(durationSeconds) {\n            let startProgress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n            }\n            const steps = Math.max(10, durationSeconds * 2); // Update every 500ms for smooth animation\n            const progressIncrement = (100 - startProgress) / steps;\n            let currentProgress = startProgress;\n            let stepCount = 0;\n            progressTimerRef.current = setInterval({\n                \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": ()=>{\n                    stepCount++;\n                    // Use easing function for more natural progress\n                    const easedProgress = startProgress + (100 - startProgress) * easeOutCubic(stepCount / steps);\n                    currentProgress = Math.min(easedProgress, 95); // Cap at 95% until manually completed\n                    updateProgress(currentProgress);\n                    if (stepCount >= steps) {\n                        if (progressTimerRef.current) {\n                            clearInterval(progressTimerRef.current);\n                            progressTimerRef.current = null;\n                        }\n                    }\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], 500);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], [\n        updateProgress\n    ]);\n    // Start orchestration tracking\n    const startTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startTracking]\": ()=>{\n            setIsActive(true);\n            setOverallProgress(0);\n            setCurrentStep(null);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startTracking]\"], []);\n    // Complete orchestration tracking\n    const completeTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setOverallProgress(100);\n            // Keep active for a bit to show completion state\n            setTimeout({\n                \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n                    setIsActive(false);\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], 3000);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], []);\n    // Reset tracking\n    const resetTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[resetTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setCurrentStep(null);\n            setIsActive(false);\n            setOverallProgress(0);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[resetTracking]\"], []);\n    return {\n        currentStep,\n        isActive,\n        overallProgress,\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        updateProgress,\n        startTracking,\n        completeTracking,\n        resetTracking\n    };\n}\n// Easing function for smooth progress animation\nfunction easeOutCubic(t) {\n    return 1 - Math.pow(1 - t, 3);\n}\n// Predefined status steps for common orchestration phases\nconst MINIMALIST_STATUS_STEPS = {\n    // Connection and initialization\n    CONNECTING: {\n        type: 'connecting',\n        title: '🔗 Establishing Connections',\n        description: 'Setting up secure connections to AI providers...',\n        icon: 'connecting',\n        color: 'orange',\n        duration: 2\n    },\n    // Analysis phase\n    ANALYZING: {\n        type: 'analyzing',\n        title: '🔍 Analyzing Your Request',\n        description: 'RouKey AI is examining your request to understand the best approach...',\n        icon: 'analysis',\n        color: 'blue',\n        duration: 3,\n        details: [\n            'Understanding request complexity and requirements',\n            'Identifying the most suitable AI approach',\n            'Preparing optimal processing strategy'\n        ]\n    },\n    // Classification\n    CLASSIFICATION: {\n        type: 'classification',\n        title: '🧠 Understanding Your Needs',\n        description: 'Determining the perfect AI specialist for your request...',\n        icon: 'classification',\n        color: 'purple',\n        duration: 3,\n        details: [\n            'Analyzing request type and complexity',\n            'Matching requirements to specialist expertise',\n            'Selecting optimal AI configuration'\n        ]\n    },\n    // Agent preparation\n    PREPARING_SPECIALIST: {\n        type: 'agent_creation',\n        title: '🤖 Preparing AI Specialist',\n        description: 'Setting up the perfect expert for your task...',\n        icon: 'agents',\n        color: 'teal',\n        duration: 4,\n        details: [\n            'Configuring AI specialist with optimal parameters',\n            'Loading relevant knowledge and capabilities',\n            'Establishing secure processing environment'\n        ]\n    },\n    // Multi-agent coordination\n    COORDINATING_TEAM: {\n        type: 'supervisor_init',\n        title: '✨ Coordinating AI Team',\n        description: 'Setting up collaborative workflow between specialists...',\n        icon: 'supervisor',\n        color: 'orange',\n        duration: 2,\n        details: [\n            'Initializing team coordination protocols',\n            'Establishing communication channels',\n            'Preparing collaborative workspace'\n        ]\n    },\n    // Active processing\n    PROCESSING: {\n        type: 'agent_working',\n        title: '⚙️ AI Specialist Working',\n        description: 'Your dedicated expert is crafting the perfect response...',\n        icon: 'working',\n        color: 'cyan',\n        duration: 15,\n        details: [\n            'Applying specialized expertise to your request',\n            'Generating high-quality, tailored content',\n            'Ensuring accuracy and relevance'\n        ]\n    },\n    // Synthesis phase\n    SYNTHESIZING: {\n        type: 'supervisor_synthesis',\n        title: '🧪 Finalizing Response',\n        description: 'Combining all insights into your perfect response...',\n        icon: 'synthesis',\n        color: 'emerald',\n        duration: 5,\n        details: [\n            'Reviewing and combining all contributions',\n            'Ensuring coherence and quality',\n            'Applying final optimizations'\n        ]\n    },\n    // Simple thinking for single-agent\n    THINKING: {\n        type: 'thinking',\n        title: '🧠 AI Thinking',\n        description: 'Processing your request and formulating the best response...',\n        icon: 'thinking',\n        color: 'purple',\n        duration: 8,\n        details: [\n            'Analyzing your request in detail',\n            'Accessing relevant knowledge and context',\n            'Crafting personalized, high-quality response'\n        ]\n    },\n    // Generation phase\n    GENERATING: {\n        type: 'generating',\n        title: '✍️ Generating Response',\n        description: 'Creating your personalized response...',\n        icon: 'generating',\n        color: 'rose',\n        duration: 10,\n        details: [\n            'Applying AI expertise to your specific needs',\n            'Generating detailed, accurate content',\n            'Ensuring quality and relevance'\n        ]\n    },\n    // Optimization\n    OPTIMIZING: {\n        type: 'optimizing',\n        title: '⚡ Optimizing Results',\n        description: 'Fine-tuning for the best possible outcome...',\n        icon: 'optimizing',\n        color: 'green',\n        duration: 3,\n        details: [\n            'Reviewing generated content for quality',\n            'Applying final optimizations',\n            'Ensuring perfect alignment with your needs'\n        ]\n    },\n    // Validation\n    VALIDATING: {\n        type: 'validating',\n        title: '✅ Quality Check',\n        description: 'Ensuring accuracy and quality of the response...',\n        icon: 'validating',\n        color: 'indigo',\n        duration: 2,\n        details: [\n            'Performing comprehensive quality review',\n            'Validating accuracy and completeness',\n            'Confirming optimal response quality'\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\n"));

/***/ })

});