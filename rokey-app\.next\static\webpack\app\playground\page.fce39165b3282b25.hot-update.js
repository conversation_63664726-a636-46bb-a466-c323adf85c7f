"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts":
/*!*******************************************************!*\
  !*** ./src/hooks/useMinimalistOrchestrationStatus.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MINIMALIST_STATUS_STEPS: () => (/* binding */ MINIMALIST_STATUS_STEPS),\n/* harmony export */   useMinimalistOrchestrationStatus: () => (/* binding */ useMinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMinimalistOrchestrationStatus,MINIMALIST_STATUS_STEPS auto */ \nfunction useMinimalistOrchestrationStatus() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [allSteps, setAllSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [overallProgress, setOverallProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const progressTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Start a new status step\n    const startStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (event)=>{\n            const newStep = {\n                id: \"step_\".concat(Date.now()),\n                title: event.title,\n                description: event.description,\n                status: 'in_progress',\n                icon: event.icon,\n                color: event.color,\n                progress: event.progress || 0,\n                details: event.details,\n                timestamp: new Date()\n            };\n            setCurrentStep(newStep);\n            setAllSteps({\n                \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (prev)=>[\n                        ...prev,\n                        newStep\n                    ]\n            }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"]);\n            setIsActive(true);\n            // Simulate progress if duration is provided\n            if (event.duration && event.duration > 0) {\n                simulateProgress(event.duration, event.progress || 0);\n            }\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"], []);\n    // Update current step\n    const updateStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (updates)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const updated = {\n                        ...prev,\n                        ...updates,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (step)=>step.id === prev.id ? updated : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n                    return updated;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"], []);\n    // Complete current step\n    const completeStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (finalDetails)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const completed = {\n                        ...prev,\n                        status: 'completed',\n                        progress: 100,\n                        details: finalDetails || prev.details,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (step)=>step.id === prev.id ? completed : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n                    return completed;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n            setOverallProgress(100);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"], []);\n    // Error in current step\n    const errorStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (error)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'error',\n                        description: error,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"], []);\n    // Update progress\n    const updateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (progress, description)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (prev)=>prev ? {\n                        ...prev,\n                        progress,\n                        ...description && {\n                            description\n                        },\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"]);\n            setOverallProgress(progress);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"], []);\n    // Simulate realistic progress over time\n    const simulateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": function(durationSeconds) {\n            let startProgress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n            }\n            const steps = Math.max(10, durationSeconds * 2); // Update every 500ms for smooth animation\n            const progressIncrement = (100 - startProgress) / steps;\n            let currentProgress = startProgress;\n            let stepCount = 0;\n            progressTimerRef.current = setInterval({\n                \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": ()=>{\n                    stepCount++;\n                    // Use easing function for more natural progress\n                    const easedProgress = startProgress + (100 - startProgress) * easeOutCubic(stepCount / steps);\n                    currentProgress = Math.min(easedProgress, 95); // Cap at 95% until manually completed\n                    updateProgress(currentProgress);\n                    if (stepCount >= steps) {\n                        if (progressTimerRef.current) {\n                            clearInterval(progressTimerRef.current);\n                            progressTimerRef.current = null;\n                        }\n                    }\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], 500);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], [\n        updateProgress\n    ]);\n    // Start orchestration tracking\n    const startTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startTracking]\": ()=>{\n            setIsActive(true);\n            setOverallProgress(0);\n            setCurrentStep(null);\n            setAllSteps([]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startTracking]\"], []);\n    // Complete orchestration tracking\n    const completeTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setOverallProgress(100);\n            // Keep active for a bit to show completion state\n            setTimeout({\n                \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n                    setIsActive(false);\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], 3000);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], []);\n    // Reset tracking\n    const resetTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[resetTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setCurrentStep(null);\n            setAllSteps([]);\n            setIsActive(false);\n            setOverallProgress(0);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[resetTracking]\"], []);\n    return {\n        currentStep,\n        allSteps,\n        isActive,\n        overallProgress,\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        updateProgress,\n        startTracking,\n        completeTracking,\n        resetTracking\n    };\n}\n// Easing function for smooth progress animation\nfunction easeOutCubic(t) {\n    return 1 - Math.pow(1 - t, 3);\n}\n// Predefined status steps for common orchestration phases\nconst MINIMALIST_STATUS_STEPS = {\n    // Connection and initialization\n    CONNECTING: {\n        type: 'connecting',\n        title: '🔗 Establishing Connections',\n        description: 'Setting up secure connections to AI providers...',\n        icon: 'connecting',\n        color: 'orange',\n        duration: 2\n    },\n    // Analysis phase\n    ANALYZING: {\n        type: 'analyzing',\n        title: '🔍 Analyzing Your Request',\n        description: 'RouKey AI is examining your request to understand the best approach...',\n        icon: 'analysis',\n        color: 'blue',\n        duration: 3,\n        details: [\n            'Understanding request complexity and requirements',\n            'Identifying the most suitable AI approach',\n            'Preparing optimal processing strategy'\n        ]\n    },\n    // Classification\n    CLASSIFICATION: {\n        type: 'classification',\n        title: '🧠 Understanding Your Needs',\n        description: 'Determining the perfect AI specialist for your request...',\n        icon: 'classification',\n        color: 'purple',\n        duration: 3,\n        details: [\n            'Analyzing request type and complexity',\n            'Matching requirements to specialist expertise',\n            'Selecting optimal AI configuration'\n        ]\n    },\n    // Agent preparation\n    PREPARING_SPECIALIST: {\n        type: 'agent_creation',\n        title: '🤖 Preparing AI Specialist',\n        description: 'Setting up the perfect expert for your task...',\n        icon: 'agents',\n        color: 'teal',\n        duration: 4,\n        details: [\n            'Configuring AI specialist with optimal parameters',\n            'Loading relevant knowledge and capabilities',\n            'Establishing secure processing environment'\n        ]\n    },\n    // Multi-agent coordination\n    COORDINATING_TEAM: {\n        type: 'supervisor_init',\n        title: '✨ Coordinating AI Team',\n        description: 'Setting up collaborative workflow between specialists...',\n        icon: 'supervisor',\n        color: 'orange',\n        duration: 2,\n        details: [\n            'Initializing team coordination protocols',\n            'Establishing communication channels',\n            'Preparing collaborative workspace'\n        ]\n    },\n    // Active processing\n    PROCESSING: {\n        type: 'agent_working',\n        title: '⚙️ AI Specialist Working',\n        description: 'Your dedicated expert is crafting the perfect response...',\n        icon: 'working',\n        color: 'cyan',\n        duration: 15,\n        details: [\n            'Applying specialized expertise to your request',\n            'Generating high-quality, tailored content',\n            'Ensuring accuracy and relevance'\n        ]\n    },\n    // Synthesis phase\n    SYNTHESIZING: {\n        type: 'supervisor_synthesis',\n        title: '🧪 Finalizing Response',\n        description: 'Combining all insights into your perfect response...',\n        icon: 'synthesis',\n        color: 'emerald',\n        duration: 5,\n        details: [\n            'Reviewing and combining all contributions',\n            'Ensuring coherence and quality',\n            'Applying final optimizations'\n        ]\n    },\n    // Simple thinking for single-agent\n    THINKING: {\n        type: 'thinking',\n        title: '🧠 AI Thinking',\n        description: 'Processing your request and formulating the best response...',\n        icon: 'thinking',\n        color: 'purple',\n        duration: 8,\n        details: [\n            'Analyzing your request in detail',\n            'Accessing relevant knowledge and context',\n            'Crafting personalized, high-quality response'\n        ]\n    },\n    // Generation phase\n    GENERATING: {\n        type: 'generating',\n        title: '✍️ Generating Response',\n        description: 'Creating your personalized response...',\n        icon: 'generating',\n        color: 'rose',\n        duration: 10,\n        details: [\n            'Applying AI expertise to your specific needs',\n            'Generating detailed, accurate content',\n            'Ensuring quality and relevance'\n        ]\n    },\n    // Optimization\n    OPTIMIZING: {\n        type: 'optimizing',\n        title: '⚡ Optimizing Results',\n        description: 'Fine-tuning for the best possible outcome...',\n        icon: 'optimizing',\n        color: 'green',\n        duration: 3,\n        details: [\n            'Reviewing generated content for quality',\n            'Applying final optimizations',\n            'Ensuring perfect alignment with your needs'\n        ]\n    },\n    // Validation\n    VALIDATING: {\n        type: 'validating',\n        title: '✅ Quality Check',\n        description: 'Ensuring accuracy and quality of the response...',\n        icon: 'validating',\n        color: 'indigo',\n        duration: 2,\n        details: [\n            'Performing comprehensive quality review',\n            'Validating accuracy and completeness',\n            'Confirming optimal response quality'\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\n"));

/***/ })

});