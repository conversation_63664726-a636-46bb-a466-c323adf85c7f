"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction SparklesIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z\"\n    }));\n}\n_c = SparklesIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(SparklesIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"SparklesIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MinimalistOrchestrationStatus.tsx":
/*!**********************************************************!*\
  !*** ./src/components/MinimalistOrchestrationStatus.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction MinimalistOrchestrationStatus(param) {\n    let { currentStep, isActive, progress = 0, className = '', showDetails = true, allSteps = [] } = param;\n    var _currentStep_details;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Auto-collapse when not active\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MinimalistOrchestrationStatus.useEffect\": ()=>{\n            if (!isActive && isExpanded) {\n                const timer = setTimeout({\n                    \"MinimalistOrchestrationStatus.useEffect.timer\": ()=>setIsExpanded(false)\n                }[\"MinimalistOrchestrationStatus.useEffect.timer\"], 2000);\n                return ({\n                    \"MinimalistOrchestrationStatus.useEffect\": ()=>clearTimeout(timer)\n                })[\"MinimalistOrchestrationStatus.useEffect\"];\n            }\n        }\n    }[\"MinimalistOrchestrationStatus.useEffect\"], [\n        isActive,\n        isExpanded\n    ]);\n    if (!isActive && !currentStep) {\n        return null;\n    }\n    // Get the main status text\n    const getStatusText = ()=>{\n        if ((currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'completed') {\n            return 'Thinking complete';\n        }\n        return (currentStep === null || currentStep === void 0 ? void 0 : currentStep.title) || 'RouKey AI is thinking';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            flex items-center space-x-3 px-4 py-3 rounded-full transition-all duration-300 cursor-pointer\\n            \".concat(isActive ? 'bg-gray-800/90 hover:bg-gray-800' : 'bg-gray-800/70 hover:bg-gray-800/80', \"\\n            backdrop-blur-sm border border-gray-700/50 hover:border-gray-600/70\\n          \"),\n                    onClick: ()=>showDetails && setIsExpanded(!isExpanded),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex items-center justify-center\",\n                            children: isActive && (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'in_progress' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-gradient-to-br from-blue-400 to-purple-500 transform rotate-45 animate-spin\",\n                                        style: {\n                                            animationDuration: '2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-4 h-4 bg-gradient-to-br from-blue-300 to-purple-400 transform rotate-45 animate-pulse opacity-60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this) : (currentStep === null || currentStep === void 0 ? void 0 : currentStep.status) === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-gradient-to-br from-green-400 to-emerald-500 transform rotate-45\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-white\",\n                            children: getStatusText()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        showDetails && (allSteps.length > 0 || (currentStep === null || currentStep === void 0 ? void 0 : (_currentStep_details = currentStep.details) === null || _currentStep_details === void 0 ? void 0 : _currentStep_details.length)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4 text-gray-400 transition-transform duration-200 \".concat(isExpanded ? 'rotate-180' : '')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 mx-auto max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 space-y-4\",\n                    children: [\n                        (currentStep === null || currentStep === void 0 ? void 0 : currentStep.details) && currentStep.details.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-300 mb-3\",\n                                    children: \"Current Process\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: currentStep.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 text-sm text-gray-400 animate-in slide-in-from-top-2 duration-300\",\n                                            style: {\n                                                animationDelay: \"\".concat(index * 100, \"ms\")\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1.5 h-1.5 rounded-full bg-blue-400 mt-2 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: detail\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 15\n                        }, this),\n                        allSteps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-300 mb-3\",\n                                    children: \"Process Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: allSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 text-sm animate-in slide-in-from-top-2 duration-300\",\n                                            style: {\n                                                animationDelay: \"\".concat(index * 50, \"ms\")\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: step.status === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 27\n                                                    }, this) : step.status === 'in_progress' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 27\n                                                    }, this) : step.status === 'error' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-red-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-gray-600 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\\n                        \".concat(step.status === 'completed' ? 'text-green-300' : step.status === 'in_progress' ? 'text-blue-300' : step.status === 'error' ? 'text-red-300' : 'text-gray-400', \"\\n                      \"),\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 23\n                                                }, this),\n                                                step.timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 ml-auto\",\n                                                    children: step.timestamp.toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, step.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationStatus.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(MinimalistOrchestrationStatus, \"Pt9BFJk6g6Zfr+NmN7bkLV1oDSM=\");\n_c = MinimalistOrchestrationStatus;\nvar _c;\n$RefreshReg$(_c, \"MinimalistOrchestrationStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MinimalistOrchestrationStatus.tsx\n"));

/***/ })

});