"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts":
/*!*******************************************************!*\
  !*** ./src/hooks/useMinimalistOrchestrationStatus.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MINIMALIST_STATUS_STEPS: () => (/* binding */ MINIMALIST_STATUS_STEPS),\n/* harmony export */   useMinimalistOrchestrationStatus: () => (/* binding */ useMinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMinimalistOrchestrationStatus,MINIMALIST_STATUS_STEPS auto */ \nfunction useMinimalistOrchestrationStatus() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [allSteps, setAllSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [overallProgress, setOverallProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const progressTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Start a new status step\n    const startStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (event)=>{\n            const newStep = {\n                id: \"step_\".concat(Date.now()),\n                title: event.title,\n                description: event.description,\n                status: 'in_progress',\n                icon: event.icon,\n                color: event.color,\n                progress: event.progress || 0,\n                details: event.details,\n                timestamp: new Date()\n            };\n            setCurrentStep(newStep);\n            setAllSteps({\n                \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (prev)=>[\n                        ...prev,\n                        newStep\n                    ]\n            }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"]);\n            setIsActive(true);\n            // Simulate progress if duration is provided\n            if (event.duration && event.duration > 0) {\n                simulateProgress(event.duration, event.progress || 0);\n            }\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"], []);\n    // Update current step\n    const updateStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (updates)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prev)=>prev ? {\n                        ...prev,\n                        ...updates,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"], []);\n    // Complete current step\n    const completeStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (finalDetails)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'completed',\n                        progress: 100,\n                        details: finalDetails || prev.details,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n            setOverallProgress(100);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"], []);\n    // Error in current step\n    const errorStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (error)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'error',\n                        description: error,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"], []);\n    // Update progress\n    const updateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (progress, description)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (prev)=>prev ? {\n                        ...prev,\n                        progress,\n                        ...description && {\n                            description\n                        },\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"]);\n            setOverallProgress(progress);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"], []);\n    // Simulate realistic progress over time\n    const simulateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": function(durationSeconds) {\n            let startProgress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n            }\n            const steps = Math.max(10, durationSeconds * 2); // Update every 500ms for smooth animation\n            const progressIncrement = (100 - startProgress) / steps;\n            let currentProgress = startProgress;\n            let stepCount = 0;\n            progressTimerRef.current = setInterval({\n                \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": ()=>{\n                    stepCount++;\n                    // Use easing function for more natural progress\n                    const easedProgress = startProgress + (100 - startProgress) * easeOutCubic(stepCount / steps);\n                    currentProgress = Math.min(easedProgress, 95); // Cap at 95% until manually completed\n                    updateProgress(currentProgress);\n                    if (stepCount >= steps) {\n                        if (progressTimerRef.current) {\n                            clearInterval(progressTimerRef.current);\n                            progressTimerRef.current = null;\n                        }\n                    }\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], 500);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], [\n        updateProgress\n    ]);\n    // Start orchestration tracking\n    const startTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startTracking]\": ()=>{\n            setIsActive(true);\n            setOverallProgress(0);\n            setCurrentStep(null);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startTracking]\"], []);\n    // Complete orchestration tracking\n    const completeTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setOverallProgress(100);\n            // Keep active for a bit to show completion state\n            setTimeout({\n                \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n                    setIsActive(false);\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], 3000);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], []);\n    // Reset tracking\n    const resetTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[resetTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setCurrentStep(null);\n            setIsActive(false);\n            setOverallProgress(0);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[resetTracking]\"], []);\n    return {\n        currentStep,\n        isActive,\n        overallProgress,\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        updateProgress,\n        startTracking,\n        completeTracking,\n        resetTracking\n    };\n}\n// Easing function for smooth progress animation\nfunction easeOutCubic(t) {\n    return 1 - Math.pow(1 - t, 3);\n}\n// Predefined status steps for common orchestration phases\nconst MINIMALIST_STATUS_STEPS = {\n    // Connection and initialization\n    CONNECTING: {\n        type: 'connecting',\n        title: '🔗 Establishing Connections',\n        description: 'Setting up secure connections to AI providers...',\n        icon: 'connecting',\n        color: 'orange',\n        duration: 2\n    },\n    // Analysis phase\n    ANALYZING: {\n        type: 'analyzing',\n        title: '🔍 Analyzing Your Request',\n        description: 'RouKey AI is examining your request to understand the best approach...',\n        icon: 'analysis',\n        color: 'blue',\n        duration: 3,\n        details: [\n            'Understanding request complexity and requirements',\n            'Identifying the most suitable AI approach',\n            'Preparing optimal processing strategy'\n        ]\n    },\n    // Classification\n    CLASSIFICATION: {\n        type: 'classification',\n        title: '🧠 Understanding Your Needs',\n        description: 'Determining the perfect AI specialist for your request...',\n        icon: 'classification',\n        color: 'purple',\n        duration: 3,\n        details: [\n            'Analyzing request type and complexity',\n            'Matching requirements to specialist expertise',\n            'Selecting optimal AI configuration'\n        ]\n    },\n    // Agent preparation\n    PREPARING_SPECIALIST: {\n        type: 'agent_creation',\n        title: '🤖 Preparing AI Specialist',\n        description: 'Setting up the perfect expert for your task...',\n        icon: 'agents',\n        color: 'teal',\n        duration: 4,\n        details: [\n            'Configuring AI specialist with optimal parameters',\n            'Loading relevant knowledge and capabilities',\n            'Establishing secure processing environment'\n        ]\n    },\n    // Multi-agent coordination\n    COORDINATING_TEAM: {\n        type: 'supervisor_init',\n        title: '✨ Coordinating AI Team',\n        description: 'Setting up collaborative workflow between specialists...',\n        icon: 'supervisor',\n        color: 'orange',\n        duration: 2,\n        details: [\n            'Initializing team coordination protocols',\n            'Establishing communication channels',\n            'Preparing collaborative workspace'\n        ]\n    },\n    // Active processing\n    PROCESSING: {\n        type: 'agent_working',\n        title: '⚙️ AI Specialist Working',\n        description: 'Your dedicated expert is crafting the perfect response...',\n        icon: 'working',\n        color: 'cyan',\n        duration: 15,\n        details: [\n            'Applying specialized expertise to your request',\n            'Generating high-quality, tailored content',\n            'Ensuring accuracy and relevance'\n        ]\n    },\n    // Synthesis phase\n    SYNTHESIZING: {\n        type: 'supervisor_synthesis',\n        title: '🧪 Finalizing Response',\n        description: 'Combining all insights into your perfect response...',\n        icon: 'synthesis',\n        color: 'emerald',\n        duration: 5,\n        details: [\n            'Reviewing and combining all contributions',\n            'Ensuring coherence and quality',\n            'Applying final optimizations'\n        ]\n    },\n    // Simple thinking for single-agent\n    THINKING: {\n        type: 'thinking',\n        title: '🧠 AI Thinking',\n        description: 'Processing your request and formulating the best response...',\n        icon: 'thinking',\n        color: 'purple',\n        duration: 8,\n        details: [\n            'Analyzing your request in detail',\n            'Accessing relevant knowledge and context',\n            'Crafting personalized, high-quality response'\n        ]\n    },\n    // Generation phase\n    GENERATING: {\n        type: 'generating',\n        title: '✍️ Generating Response',\n        description: 'Creating your personalized response...',\n        icon: 'generating',\n        color: 'rose',\n        duration: 10,\n        details: [\n            'Applying AI expertise to your specific needs',\n            'Generating detailed, accurate content',\n            'Ensuring quality and relevance'\n        ]\n    },\n    // Optimization\n    OPTIMIZING: {\n        type: 'optimizing',\n        title: '⚡ Optimizing Results',\n        description: 'Fine-tuning for the best possible outcome...',\n        icon: 'optimizing',\n        color: 'green',\n        duration: 3,\n        details: [\n            'Reviewing generated content for quality',\n            'Applying final optimizations',\n            'Ensuring perfect alignment with your needs'\n        ]\n    },\n    // Validation\n    VALIDATING: {\n        type: 'validating',\n        title: '✅ Quality Check',\n        description: 'Ensuring accuracy and quality of the response...',\n        icon: 'validating',\n        color: 'indigo',\n        duration: 2,\n        details: [\n            'Performing comprehensive quality review',\n            'Validating accuracy and completeness',\n            'Confirming optimal response quality'\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\n"));

/***/ })

});