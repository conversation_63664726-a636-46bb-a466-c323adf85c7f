"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/MinimalistOrchestrationBridge.tsx":
/*!**********************************************************!*\
  !*** ./src/components/MinimalistOrchestrationBridge.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MinimalistOrchestrationBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MinimalistOrchestrationStatus */ \"(app-pages-browser)/./src/components/MinimalistOrchestrationStatus.tsx\");\n/* harmony import */ var _hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useMinimalistOrchestrationStatus */ \"(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MinimalistOrchestrationBridge(param) {\n    let { isActive, onProgressCallback, className = '', showDetails = true } = param;\n    _s();\n    console.log('🎯 MinimalistOrchestrationBridge render:', {\n        isActive,\n        className\n    });\n    const { currentStep, isActive: statusActive, overallProgress, startStep, updateStep, completeStep, errorStep, updateProgress, startTracking, completeTracking, resetTracking } = (0,_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.useMinimalistOrchestrationStatus)();\n    console.log('🎯 Minimalist status state:', {\n        currentStep: currentStep === null || currentStep === void 0 ? void 0 : currentStep.title,\n        statusActive,\n        overallProgress\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.CLASSIFICATION);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep([\n                    \"✨ Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83C\\uDFAF Analysis completed with \".concat(Math.round(threshold * 100), \"% confidence\"),\n                    \"\\uD83D\\uDE80 Ready to assemble your AI dream team\"\n                ]);\n                // Transition to next step\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                        if (roles.length > 1) {\n                            startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.COORDINATING_TEAM);\n                        } else {\n                            startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);\n                        }\n                    }\n                }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"], 1000);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                updateStep({\n                    description: \"Selected \".concat(selectedRoles.length, \" AI specialists for optimal collaboration\"),\n                    details: [\n                        \"\\uD83C\\uDFAF \".concat(selectedRoles.length, \" specialists selected\"),\n                        \"⚡ Each expert optimized for your specific needs\",\n                        \"\\uD83E\\uDD1D Team ready for collaborative processing\"\n                    ]\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                const workflowNames = {\n                    'sequential': 'step-by-step collaboration',\n                    'supervisor': 'coordinated teamwork',\n                    'hierarchical': 'multi-level coordination',\n                    'parallel': 'simultaneous processing'\n                };\n                updateStep({\n                    description: \"Configured for \".concat(workflowNames[workflowType] || 'smart collaboration'),\n                    details: [\n                        \"\\uD83C\\uDFAF Strategy: \".concat(workflowNames[workflowType] || workflowType),\n                        \"\\uD83E\\uDDE0 Optimized for your specific request type\",\n                        \"⚡ Team coordination plan established\"\n                    ]\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                if ((currentStep === null || currentStep === void 0 ? void 0 : currentStep.title) !== '🤖 Preparing AI Specialist') {\n                    startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);\n                }\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep([\n                    \"\\uD83E\\uDD16 \".concat(agents.length, \" AI specialist\").concat(agents.length > 1 ? 's' : '', \" ready to work\"),\n                    \"⚙️ Expert\".concat(agents.length > 1 ? 's' : '', \" configured with optimal settings\"),\n                    \"\\uD83D\\uDE80 Ready to begin processing your request\"\n                ]);\n                // Transition to processing\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                        startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PROCESSING);\n                    }\n                }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"], 1000);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                if ((currentStep === null || currentStep === void 0 ? void 0 : currentStep.title) !== '✨ Coordinating AI Team') {\n                    startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.COORDINATING_TEAM);\n                }\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep([\n                    '✨ Team coordinator activated',\n                    '📡 Communication channels established',\n                    '🎯 Ready for collaborative processing'\n                ]);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                updateStep({\n                    description: 'Breaking down your request into specialized tasks...',\n                    progress: 20\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (plan)=>{\n                updateStep({\n                    description: 'Task planning completed, beginning specialist work...',\n                    progress: 40\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': '💡 Creative Ideation Specialist',\n                    'writing': '✍️ Content Writing Expert',\n                    'coding_backend': '⚙️ Backend Development Expert',\n                    'coding_frontend': '🎨 Frontend Development Expert',\n                    'general_chat': '🤖 General AI Assistant'\n                };\n                const agentName = roleNames[role] || '🤖 AI Specialist';\n                // Update current processing step with agent info\n                updateStep({\n                    title: \"\".concat(agentName, \" Working\"),\n                    description: task,\n                    details: [\n                        \"\\uD83C\\uDFAF Specialist: \".concat(agentName),\n                        '🧠 Applying deep expertise to your request',\n                        '⚡ Generating high-quality, tailored response'\n                    ]\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (role, result)=>{\n                updateStep({\n                    description: 'Specialist work completed, preparing final response...',\n                    progress: 80\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.SYNTHESIZING);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep([\n                    '✅ Final response synthesis completed',\n                    '🎯 All specialist insights successfully combined',\n                    '🚀 Your response is ready!'\n                ]);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (result)=>{\n                // Final validation step\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                        startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.VALIDATING);\n                        setTimeout({\n                            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                                completeStep([\n                                    '✅ Quality validation completed',\n                                    '🎯 Response meets all requirements',\n                                    '🚀 Orchestration successful!'\n                                ]);\n                                completeTracking();\n                            }\n                        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"], 1500);\n                    }\n                }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"], 500);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (step, error)=>{\n                errorStep(\"Error in \".concat(step, \": \").concat(error));\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"]\n    }, [\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        completeTracking,\n        currentStep\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"MinimalistOrchestrationBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Enhanced start/stop tracking with realistic flow\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n            if (isActive && !statusActive) {\n                startTracking();\n                // Start with connection step\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                        startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.CONNECTING);\n                    }\n                }[\"MinimalistOrchestrationBridge.useEffect\"], 300);\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                        completeStep([\n                            '🔗 Secure connections established',\n                            '⚡ Ready to begin analysis'\n                        ]);\n                        // Move to analysis\n                        setTimeout({\n                            \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.ANALYZING);\n                            }\n                        }[\"MinimalistOrchestrationBridge.useEffect\"], 800);\n                        setTimeout({\n                            \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                completeStep([\n                                    '🎯 Request analysis completed',\n                                    '🧠 Optimal approach identified',\n                                    '🚀 Ready to process your request'\n                                ]);\n                                // Move to specialist preparation\n                                setTimeout({\n                                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                        startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);\n                                    }\n                                }[\"MinimalistOrchestrationBridge.useEffect\"], 800);\n                                setTimeout({\n                                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                        completeStep([\n                                            '🤖 AI specialist ready to work',\n                                            '⚙️ Expert configured with optimal settings',\n                                            '⚡ Beginning processing...'\n                                        ]);\n                                        // Move to processing\n                                        setTimeout({\n                                            \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                                startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PROCESSING);\n                                            }\n                                        }[\"MinimalistOrchestrationBridge.useEffect\"], 800);\n                                    }\n                                }[\"MinimalistOrchestrationBridge.useEffect\"], 3000);\n                            }\n                        }[\"MinimalistOrchestrationBridge.useEffect\"], 2500);\n                    }\n                }[\"MinimalistOrchestrationBridge.useEffect\"], 1500);\n            } else if (!isActive && statusActive) {\n                // Delay before resetting to show final state\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"MinimalistOrchestrationBridge.useEffect\"], 4000);\n            }\n        }\n    }[\"MinimalistOrchestrationBridge.useEffect\"], [\n        isActive,\n        statusActive,\n        startTracking,\n        resetTracking,\n        startStep,\n        completeStep\n    ]);\n    console.log('🎯 Minimalist render decision:', {\n        statusActive,\n        currentStep: !!currentStep,\n        shouldRender: statusActive && currentStep\n    });\n    if (!statusActive || !currentStep) {\n        console.log('🎯 Not rendering minimalist - statusActive:', statusActive, 'currentStep:', !!currentStep);\n        return null;\n    }\n    console.log('🎯 Rendering minimalist status:', currentStep.title);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            currentStep: currentStep,\n            isActive: statusActive,\n            progress: overallProgress,\n            showDetails: showDetails,\n            allSteps: allSteps\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationBridge.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationBridge.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\n_s(MinimalistOrchestrationBridge, \"/OKs3M6mnJjPxhlsr94fnUrrU+Q=\", false, function() {\n    return [\n        _hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.useMinimalistOrchestrationStatus\n    ];\n});\n_c = MinimalistOrchestrationBridge;\nvar _c;\n$RefreshReg$(_c, \"MinimalistOrchestrationBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01pbmltYWxpc3RPcmNoZXN0cmF0aW9uQnJpZGdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVzRDtBQUNzQjtBQUN5QztBQVV0RyxTQUFTTSw4QkFBOEIsS0FLakI7UUFMaUIsRUFDcERDLFFBQVEsRUFDUkMsa0JBQWtCLEVBQ2xCQyxZQUFZLEVBQUUsRUFDZEMsY0FBYyxJQUFJLEVBQ2lCLEdBTGlCOztJQU1wREMsUUFBUUMsR0FBRyxDQUFDLDRDQUE0QztRQUFFTDtRQUFVRTtJQUFVO0lBRTlFLE1BQU0sRUFDSkksV0FBVyxFQUNYTixVQUFVTyxZQUFZLEVBQ3RCQyxlQUFlLEVBQ2ZDLFNBQVMsRUFDVEMsVUFBVSxFQUNWQyxZQUFZLEVBQ1pDLFNBQVMsRUFDVEMsY0FBYyxFQUNkQyxhQUFhLEVBQ2JDLGdCQUFnQixFQUNoQkMsYUFBYSxFQUNkLEdBQUduQix5R0FBZ0NBO0lBRXBDTyxRQUFRQyxHQUFHLENBQUMsK0JBQStCO1FBQUVDLFdBQVcsRUFBRUEsd0JBQUFBLGtDQUFBQSxZQUFhVyxLQUFLO1FBQUVWO1FBQWNDO0lBQWdCO0lBRTVHLG9EQUFvRDtJQUNwRCxNQUFNVSxtQkFBcUN2QixrREFBV0EsQ0FBQztRQUNyRHdCLHFCQUFxQjsyRUFBRTtnQkFDckJWLFVBQVVYLDRGQUF1QkEsQ0FBQ3NCLGNBQWM7WUFDbEQ7O1FBRUFDLHdCQUF3QjsyRUFBRSxDQUFDQyxPQUFpQkM7Z0JBQzFDWixhQUFhO29CQUNWLGdCQUE0QixPQUFiVyxNQUFNRSxNQUFNLEVBQUM7b0JBQzVCLHdDQUF5RCxPQUE1QkMsS0FBS0MsS0FBSyxDQUFDSCxZQUFZLE1BQUs7b0JBQ3pEO2lCQUNGO2dCQUVELDBCQUEwQjtnQkFDMUJJO21GQUFXO3dCQUNULElBQUlMLE1BQU1FLE1BQU0sR0FBRyxHQUFHOzRCQUNwQmYsVUFBVVgsNEZBQXVCQSxDQUFDOEIsaUJBQWlCO3dCQUNyRCxPQUFPOzRCQUNMbkIsVUFBVVgsNEZBQXVCQSxDQUFDK0Isb0JBQW9CO3dCQUN4RDtvQkFDRjtrRkFBRztZQUNMOztRQUVBQyx1QkFBdUI7MkVBQUUsQ0FBQ0MsZUFBeUJDO2dCQUNqRHRCLFdBQVc7b0JBQ1R1QixhQUFhLFlBQWlDLE9BQXJCRixjQUFjUCxNQUFNLEVBQUM7b0JBQzlDVSxTQUFTO3dCQUNOLGdCQUEwQixPQUFyQkgsY0FBY1AsTUFBTSxFQUFDO3dCQUMxQjt3QkFDQTtxQkFDRjtnQkFDSDtZQUNGOztRQUVBVywyQkFBMkI7MkVBQUUsQ0FBQ0MsY0FBc0JDO2dCQUNsRCxNQUFNQyxnQkFBZ0I7b0JBQ3BCLGNBQWM7b0JBQ2QsY0FBYztvQkFDZCxnQkFBZ0I7b0JBQ2hCLFlBQVk7Z0JBQ2Q7Z0JBRUE1QixXQUFXO29CQUNUdUIsYUFBYSxrQkFBcUcsT0FBbkZLLGFBQWEsQ0FBQ0YsYUFBMkMsSUFBSTtvQkFDNUZGLFNBQVM7d0JBQ04sMEJBQXlGLE9BQTFFSSxhQUFhLENBQUNGLGFBQTJDLElBQUlBO3dCQUM1RTt3QkFDQTtxQkFDRjtnQkFDSDtZQUNGOztRQUVBRyxvQkFBb0I7MkVBQUU7Z0JBQ3BCLElBQUlqQyxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFXLEtBQUssTUFBSyw4QkFBOEI7b0JBQ3ZEUixVQUFVWCw0RkFBdUJBLENBQUMrQixvQkFBb0I7Z0JBQ3hEO1lBQ0Y7O1FBRUFXLHVCQUF1QjsyRUFBRSxDQUFDQztnQkFDeEI5QixhQUFhO29CQUNWLGdCQUFtQzhCLE9BQTlCQSxPQUFPakIsTUFBTSxFQUFDLGtCQUE2QyxPQUE3QmlCLE9BQU9qQixNQUFNLEdBQUcsSUFBSSxNQUFNLElBQUc7b0JBQ2hFLFlBQXdDLE9BQTdCaUIsT0FBT2pCLE1BQU0sR0FBRyxJQUFJLE1BQU0sSUFBRztvQkFDeEM7aUJBQ0Y7Z0JBRUQsMkJBQTJCO2dCQUMzQkc7bUZBQVc7d0JBQ1RsQixVQUFVWCw0RkFBdUJBLENBQUM0QyxVQUFVO29CQUM5QztrRkFBRztZQUNMOztRQUVBQyxxQkFBcUI7MkVBQUU7Z0JBQ3JCLElBQUlyQyxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFXLEtBQUssTUFBSywwQkFBMEI7b0JBQ25EUixVQUFVWCw0RkFBdUJBLENBQUM4QixpQkFBaUI7Z0JBQ3JEO1lBQ0Y7O1FBRUFnQix3QkFBd0I7MkVBQUUsQ0FBQ0M7Z0JBQ3pCbEMsYUFBYTtvQkFDWDtvQkFDQTtvQkFDQTtpQkFDRDtZQUNIOztRQUVBbUMsbUJBQW1COzJFQUFFO2dCQUNuQnBDLFdBQVc7b0JBQ1R1QixhQUFhO29CQUNiYyxVQUFVO2dCQUNaO1lBQ0Y7O1FBRUFDLHNCQUFzQjsyRUFBRSxDQUFDQztnQkFDdkJ2QyxXQUFXO29CQUNUdUIsYUFBYTtvQkFDYmMsVUFBVTtnQkFDWjtZQUNGOztRQUVBRyxnQkFBZ0I7MkVBQUUsQ0FBQ0MsTUFBY0M7Z0JBQy9CLE1BQU1DLFlBQVk7b0JBQ2hCLDBCQUEwQjtvQkFDMUIsV0FBVztvQkFDWCxrQkFBa0I7b0JBQ2xCLG1CQUFtQjtvQkFDbkIsZ0JBQWdCO2dCQUNsQjtnQkFFQSxNQUFNQyxZQUFZRCxTQUFTLENBQUNGLEtBQStCLElBQUk7Z0JBRS9ELGlEQUFpRDtnQkFDakR6QyxXQUFXO29CQUNUTyxPQUFPLEdBQWEsT0FBVnFDLFdBQVU7b0JBQ3BCckIsYUFBYW1CO29CQUNibEIsU0FBUzt3QkFDTiw0QkFBMkIsT0FBVm9CO3dCQUNsQjt3QkFDQTtxQkFDRDtnQkFDSDtZQUNGOztRQUVBQyxtQkFBbUI7MkVBQUUsQ0FBQ0osTUFBY0s7Z0JBQ2xDOUMsV0FBVztvQkFDVHVCLGFBQWE7b0JBQ2JjLFVBQVU7Z0JBQ1o7WUFDRjs7UUFFQVUsMEJBQTBCOzJFQUFFO2dCQUMxQmhELFVBQVVYLDRGQUF1QkEsQ0FBQzRELFlBQVk7WUFDaEQ7O1FBRUFDLDZCQUE2QjsyRUFBRSxDQUFDQztnQkFDOUJqRCxhQUFhO29CQUNYO29CQUNBO29CQUNBO2lCQUNEO1lBQ0g7O1FBRUFrRCx1QkFBdUI7MkVBQUUsQ0FBQ0w7Z0JBQ3hCLHdCQUF3QjtnQkFDeEI3QjttRkFBVzt3QkFDVGxCLFVBQVVYLDRGQUF1QkEsQ0FBQ2dFLFVBQVU7d0JBQzVDbkM7MkZBQVc7Z0NBQ1RoQixhQUFhO29DQUNYO29DQUNBO29DQUNBO2lDQUNEO2dDQUNESTs0QkFDRjswRkFBRztvQkFDTDtrRkFBRztZQUNMOztRQUVBZ0QsT0FBTzsyRUFBRSxDQUFDQyxNQUFjQztnQkFDdEJyRCxVQUFVLFlBQXFCcUQsT0FBVEQsTUFBSyxNQUFVLE9BQU5DO1lBQ2pDOztJQUNGLEdBQUc7UUFBQ3hEO1FBQVdDO1FBQVlDO1FBQWNDO1FBQVdHO1FBQWtCVDtLQUFZO0lBRWxGLHNDQUFzQztJQUN0Q1osZ0RBQVNBO21EQUFDO1lBQ1IsSUFBSU8sb0JBQW9CO2dCQUN0QkEsbUJBQW1CaUI7WUFDckI7UUFDRjtrREFBRztRQUFDakI7UUFBb0JpQjtLQUFpQjtJQUV6QyxtREFBbUQ7SUFDbkR4QixnREFBU0E7bURBQUM7WUFDUixJQUFJTSxZQUFZLENBQUNPLGNBQWM7Z0JBQzdCTztnQkFFQSw2QkFBNkI7Z0JBQzdCYTsrREFBVzt3QkFDVGxCLFVBQVVYLDRGQUF1QkEsQ0FBQ29FLFVBQVU7b0JBQzlDOzhEQUFHO2dCQUVIdkM7K0RBQVc7d0JBQ1RoQixhQUFhOzRCQUFDOzRCQUFxQzt5QkFBNEI7d0JBRS9FLG1CQUFtQjt3QkFDbkJnQjt1RUFBVztnQ0FDVGxCLFVBQVVYLDRGQUF1QkEsQ0FBQ3FFLFNBQVM7NEJBQzdDO3NFQUFHO3dCQUVIeEM7dUVBQVc7Z0NBQ1RoQixhQUFhO29DQUNYO29DQUNBO29DQUNBO2lDQUNEO2dDQUVELGlDQUFpQztnQ0FDakNnQjsrRUFBVzt3Q0FDVGxCLFVBQVVYLDRGQUF1QkEsQ0FBQytCLG9CQUFvQjtvQ0FDeEQ7OEVBQUc7Z0NBRUhGOytFQUFXO3dDQUNUaEIsYUFBYTs0Q0FDWDs0Q0FDQTs0Q0FDQTt5Q0FDRDt3Q0FFRCxxQkFBcUI7d0NBQ3JCZ0I7dUZBQVc7Z0RBQ1RsQixVQUFVWCw0RkFBdUJBLENBQUM0QyxVQUFVOzRDQUM5QztzRkFBRztvQ0FDTDs4RUFBRzs0QkFDTDtzRUFBRztvQkFDTDs4REFBRztZQUVMLE9BQU8sSUFBSSxDQUFDMUMsWUFBWU8sY0FBYztnQkFDcEMsNkNBQTZDO2dCQUM3Q29COytEQUFXO3dCQUNUWDtvQkFDRjs4REFBRztZQUNMO1FBQ0Y7a0RBQUc7UUFBQ2hCO1FBQVVPO1FBQWNPO1FBQWVFO1FBQWVQO1FBQVdFO0tBQWE7SUFFbEZQLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0M7UUFBRUU7UUFBY0QsYUFBYSxDQUFDLENBQUNBO1FBQWE4RCxjQUFjN0QsZ0JBQWdCRDtJQUFZO0lBRXBJLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUNELGFBQWE7UUFDakNGLFFBQVFDLEdBQUcsQ0FBQywrQ0FBK0NFLGNBQWMsZ0JBQWdCLENBQUMsQ0FBQ0Q7UUFDM0YsT0FBTztJQUNUO0lBRUFGLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNDLFlBQVlXLEtBQUs7SUFFaEUscUJBQ0UsOERBQUNvRDtRQUFJbkUsV0FBV0E7a0JBQ2QsNEVBQUNOLHNFQUE2QkE7WUFDNUJVLGFBQWFBO1lBQ2JOLFVBQVVPO1lBQ1Z3QyxVQUFVdkM7WUFDVkwsYUFBYUE7WUFDYm1FLFVBQVVBOzs7Ozs7Ozs7OztBQUlsQjtHQXpRd0J2RTs7UUFvQmxCRixxR0FBZ0NBOzs7S0FwQmRFIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcTWluaW1hbGlzdE9yY2hlc3RyYXRpb25CcmlkZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTWluaW1hbGlzdE9yY2hlc3RyYXRpb25TdGF0dXMgZnJvbSAnLi9NaW5pbWFsaXN0T3JjaGVzdHJhdGlvblN0YXR1cyc7XG5pbXBvcnQgeyB1c2VNaW5pbWFsaXN0T3JjaGVzdHJhdGlvblN0YXR1cywgTUlOSU1BTElTVF9TVEFUVVNfU1RFUFMgfSBmcm9tICdAL2hvb2tzL3VzZU1pbmltYWxpc3RPcmNoZXN0cmF0aW9uU3RhdHVzJztcbmltcG9ydCB7IHR5cGUgUHJvZ3Jlc3NDYWxsYmFjayB9IGZyb20gJ0AvbGliL2xhbmdncmFwaC1vcmNoZXN0cmF0aW9uL1JvdUtleUxhbmdHcmFwaEludGVncmF0aW9uJztcblxuaW50ZXJmYWNlIE1pbmltYWxpc3RPcmNoZXN0cmF0aW9uQnJpZGdlUHJvcHMge1xuICBpc0FjdGl2ZTogYm9vbGVhbjtcbiAgb25Qcm9ncmVzc0NhbGxiYWNrPzogKGNhbGxiYWNrOiBQcm9ncmVzc0NhbGxiYWNrKSA9PiB2b2lkO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHNob3dEZXRhaWxzPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWluaW1hbGlzdE9yY2hlc3RyYXRpb25CcmlkZ2Uoe1xuICBpc0FjdGl2ZSxcbiAgb25Qcm9ncmVzc0NhbGxiYWNrLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgc2hvd0RldGFpbHMgPSB0cnVlXG59OiBNaW5pbWFsaXN0T3JjaGVzdHJhdGlvbkJyaWRnZVByb3BzKSB7XG4gIGNvbnNvbGUubG9nKCfwn46vIE1pbmltYWxpc3RPcmNoZXN0cmF0aW9uQnJpZGdlIHJlbmRlcjonLCB7IGlzQWN0aXZlLCBjbGFzc05hbWUgfSk7XG5cbiAgY29uc3Qge1xuICAgIGN1cnJlbnRTdGVwLFxuICAgIGlzQWN0aXZlOiBzdGF0dXNBY3RpdmUsXG4gICAgb3ZlcmFsbFByb2dyZXNzLFxuICAgIHN0YXJ0U3RlcCxcbiAgICB1cGRhdGVTdGVwLFxuICAgIGNvbXBsZXRlU3RlcCxcbiAgICBlcnJvclN0ZXAsXG4gICAgdXBkYXRlUHJvZ3Jlc3MsXG4gICAgc3RhcnRUcmFja2luZyxcbiAgICBjb21wbGV0ZVRyYWNraW5nLFxuICAgIHJlc2V0VHJhY2tpbmdcbiAgfSA9IHVzZU1pbmltYWxpc3RPcmNoZXN0cmF0aW9uU3RhdHVzKCk7XG5cbiAgY29uc29sZS5sb2coJ/Cfjq8gTWluaW1hbGlzdCBzdGF0dXMgc3RhdGU6JywgeyBjdXJyZW50U3RlcDogY3VycmVudFN0ZXA/LnRpdGxlLCBzdGF0dXNBY3RpdmUsIG92ZXJhbGxQcm9ncmVzcyB9KTtcblxuICAvLyBDcmVhdGUgcHJvZ3Jlc3MgY2FsbGJhY2sgZm9yIG9yY2hlc3RyYXRpb24gc3lzdGVtXG4gIGNvbnN0IHByb2dyZXNzQ2FsbGJhY2s6IFByb2dyZXNzQ2FsbGJhY2sgPSB1c2VDYWxsYmFjayh7XG4gICAgb25DbGFzc2lmaWNhdGlvblN0YXJ0OiAoKSA9PiB7XG4gICAgICBzdGFydFN0ZXAoTUlOSU1BTElTVF9TVEFUVVNfU1RFUFMuQ0xBU1NJRklDQVRJT04pO1xuICAgIH0sXG4gICAgXG4gICAgb25DbGFzc2lmaWNhdGlvbkNvbXBsZXRlOiAocm9sZXM6IHN0cmluZ1tdLCB0aHJlc2hvbGQ6IG51bWJlcikgPT4ge1xuICAgICAgY29tcGxldGVTdGVwKFtcbiAgICAgICAgYOKcqCBJZGVudGlmaWVkICR7cm9sZXMubGVuZ3RofSBzcGVjaWFsaXN0IGFyZWFzIG5lZWRlZGAsXG4gICAgICAgIGDwn46vIEFuYWx5c2lzIGNvbXBsZXRlZCB3aXRoICR7TWF0aC5yb3VuZCh0aHJlc2hvbGQgKiAxMDApfSUgY29uZmlkZW5jZWAsXG4gICAgICAgIGDwn5qAIFJlYWR5IHRvIGFzc2VtYmxlIHlvdXIgQUkgZHJlYW0gdGVhbWBcbiAgICAgIF0pO1xuICAgICAgXG4gICAgICAvLyBUcmFuc2l0aW9uIHRvIG5leHQgc3RlcFxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGlmIChyb2xlcy5sZW5ndGggPiAxKSB7XG4gICAgICAgICAgc3RhcnRTdGVwKE1JTklNQUxJU1RfU1RBVFVTX1NURVBTLkNPT1JESU5BVElOR19URUFNKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBzdGFydFN0ZXAoTUlOSU1BTElTVF9TVEFUVVNfU1RFUFMuUFJFUEFSSU5HX1NQRUNJQUxJU1QpO1xuICAgICAgICB9XG4gICAgICB9LCAxMDAwKTtcbiAgICB9LFxuICAgIFxuICAgIG9uUm9sZVNlbGVjdGlvbkNvbXBsZXRlOiAoc2VsZWN0ZWRSb2xlczogc3RyaW5nW10sIGZpbHRlcmVkUm9sZXM6IHN0cmluZ1tdKSA9PiB7XG4gICAgICB1cGRhdGVTdGVwKHtcbiAgICAgICAgZGVzY3JpcHRpb246IGBTZWxlY3RlZCAke3NlbGVjdGVkUm9sZXMubGVuZ3RofSBBSSBzcGVjaWFsaXN0cyBmb3Igb3B0aW1hbCBjb2xsYWJvcmF0aW9uYCxcbiAgICAgICAgZGV0YWlsczogW1xuICAgICAgICAgIGDwn46vICR7c2VsZWN0ZWRSb2xlcy5sZW5ndGh9IHNwZWNpYWxpc3RzIHNlbGVjdGVkYCxcbiAgICAgICAgICBg4pqhIEVhY2ggZXhwZXJ0IG9wdGltaXplZCBmb3IgeW91ciBzcGVjaWZpYyBuZWVkc2AsXG4gICAgICAgICAgYPCfpJ0gVGVhbSByZWFkeSBmb3IgY29sbGFib3JhdGl2ZSBwcm9jZXNzaW5nYFxuICAgICAgICBdXG4gICAgICB9KTtcbiAgICB9LFxuICAgIFxuICAgIG9uV29ya2Zsb3dTZWxlY3Rpb25Db21wbGV0ZTogKHdvcmtmbG93VHlwZTogc3RyaW5nLCByZWFzb25pbmc6IHN0cmluZykgPT4ge1xuICAgICAgY29uc3Qgd29ya2Zsb3dOYW1lcyA9IHtcbiAgICAgICAgJ3NlcXVlbnRpYWwnOiAnc3RlcC1ieS1zdGVwIGNvbGxhYm9yYXRpb24nLFxuICAgICAgICAnc3VwZXJ2aXNvcic6ICdjb29yZGluYXRlZCB0ZWFtd29yaycsXG4gICAgICAgICdoaWVyYXJjaGljYWwnOiAnbXVsdGktbGV2ZWwgY29vcmRpbmF0aW9uJyxcbiAgICAgICAgJ3BhcmFsbGVsJzogJ3NpbXVsdGFuZW91cyBwcm9jZXNzaW5nJ1xuICAgICAgfTtcbiAgICAgIFxuICAgICAgdXBkYXRlU3RlcCh7XG4gICAgICAgIGRlc2NyaXB0aW9uOiBgQ29uZmlndXJlZCBmb3IgJHt3b3JrZmxvd05hbWVzW3dvcmtmbG93VHlwZSBhcyBrZXlvZiB0eXBlb2Ygd29ya2Zsb3dOYW1lc10gfHwgJ3NtYXJ0IGNvbGxhYm9yYXRpb24nfWAsXG4gICAgICAgIGRldGFpbHM6IFtcbiAgICAgICAgICBg8J+OryBTdHJhdGVneTogJHt3b3JrZmxvd05hbWVzW3dvcmtmbG93VHlwZSBhcyBrZXlvZiB0eXBlb2Ygd29ya2Zsb3dOYW1lc10gfHwgd29ya2Zsb3dUeXBlfWAsXG4gICAgICAgICAgYPCfp6AgT3B0aW1pemVkIGZvciB5b3VyIHNwZWNpZmljIHJlcXVlc3QgdHlwZWAsXG4gICAgICAgICAgYOKaoSBUZWFtIGNvb3JkaW5hdGlvbiBwbGFuIGVzdGFibGlzaGVkYFxuICAgICAgICBdXG4gICAgICB9KTtcbiAgICB9LFxuICAgIFxuICAgIG9uQWdlbnRDcmVhdGlvblN0YXJ0OiAoKSA9PiB7XG4gICAgICBpZiAoY3VycmVudFN0ZXA/LnRpdGxlICE9PSAn8J+kliBQcmVwYXJpbmcgQUkgU3BlY2lhbGlzdCcpIHtcbiAgICAgICAgc3RhcnRTdGVwKE1JTklNQUxJU1RfU1RBVFVTX1NURVBTLlBSRVBBUklOR19TUEVDSUFMSVNUKTtcbiAgICAgIH1cbiAgICB9LFxuICAgIFxuICAgIG9uQWdlbnRDcmVhdGlvbkNvbXBsZXRlOiAoYWdlbnRzOiBBcnJheTx7IHJvbGU6IHN0cmluZywgYXBpS2V5OiBzdHJpbmcgfT4pID0+IHtcbiAgICAgIGNvbXBsZXRlU3RlcChbXG4gICAgICAgIGDwn6SWICR7YWdlbnRzLmxlbmd0aH0gQUkgc3BlY2lhbGlzdCR7YWdlbnRzLmxlbmd0aCA+IDEgPyAncycgOiAnJ30gcmVhZHkgdG8gd29ya2AsXG4gICAgICAgIGDimpnvuI8gRXhwZXJ0JHthZ2VudHMubGVuZ3RoID4gMSA/ICdzJyA6ICcnfSBjb25maWd1cmVkIHdpdGggb3B0aW1hbCBzZXR0aW5nc2AsXG4gICAgICAgIGDwn5qAIFJlYWR5IHRvIGJlZ2luIHByb2Nlc3NpbmcgeW91ciByZXF1ZXN0YFxuICAgICAgXSk7XG4gICAgICBcbiAgICAgIC8vIFRyYW5zaXRpb24gdG8gcHJvY2Vzc2luZ1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHN0YXJ0U3RlcChNSU5JTUFMSVNUX1NUQVRVU19TVEVQUy5QUk9DRVNTSU5HKTtcbiAgICAgIH0sIDEwMDApO1xuICAgIH0sXG4gICAgXG4gICAgb25TdXBlcnZpc29ySW5pdFN0YXJ0OiAoKSA9PiB7XG4gICAgICBpZiAoY3VycmVudFN0ZXA/LnRpdGxlICE9PSAn4pyoIENvb3JkaW5hdGluZyBBSSBUZWFtJykge1xuICAgICAgICBzdGFydFN0ZXAoTUlOSU1BTElTVF9TVEFUVVNfU1RFUFMuQ09PUkRJTkFUSU5HX1RFQU0pO1xuICAgICAgfVxuICAgIH0sXG4gICAgXG4gICAgb25TdXBlcnZpc29ySW5pdENvbXBsZXRlOiAoc3VwZXJ2aXNvclJvbGU6IHN0cmluZykgPT4ge1xuICAgICAgY29tcGxldGVTdGVwKFtcbiAgICAgICAgJ+KcqCBUZWFtIGNvb3JkaW5hdG9yIGFjdGl2YXRlZCcsXG4gICAgICAgICfwn5OhIENvbW11bmljYXRpb24gY2hhbm5lbHMgZXN0YWJsaXNoZWQnLFxuICAgICAgICAn8J+OryBSZWFkeSBmb3IgY29sbGFib3JhdGl2ZSBwcm9jZXNzaW5nJ1xuICAgICAgXSk7XG4gICAgfSxcbiAgICBcbiAgICBvblRhc2tQbGFubmluZ1N0YXJ0OiAoKSA9PiB7XG4gICAgICB1cGRhdGVTdGVwKHtcbiAgICAgICAgZGVzY3JpcHRpb246ICdCcmVha2luZyBkb3duIHlvdXIgcmVxdWVzdCBpbnRvIHNwZWNpYWxpemVkIHRhc2tzLi4uJyxcbiAgICAgICAgcHJvZ3Jlc3M6IDIwXG4gICAgICB9KTtcbiAgICB9LFxuICAgIFxuICAgIG9uVGFza1BsYW5uaW5nQ29tcGxldGU6IChwbGFuOiBzdHJpbmcpID0+IHtcbiAgICAgIHVwZGF0ZVN0ZXAoe1xuICAgICAgICBkZXNjcmlwdGlvbjogJ1Rhc2sgcGxhbm5pbmcgY29tcGxldGVkLCBiZWdpbm5pbmcgc3BlY2lhbGlzdCB3b3JrLi4uJyxcbiAgICAgICAgcHJvZ3Jlc3M6IDQwXG4gICAgICB9KTtcbiAgICB9LFxuICAgIFxuICAgIG9uQWdlbnRXb3JrU3RhcnQ6IChyb2xlOiBzdHJpbmcsIHRhc2s6IHN0cmluZykgPT4ge1xuICAgICAgY29uc3Qgcm9sZU5hbWVzID0ge1xuICAgICAgICAnYnJhaW5zdG9ybWluZ19pZGVhdGlvbic6ICfwn5KhIENyZWF0aXZlIElkZWF0aW9uIFNwZWNpYWxpc3QnLFxuICAgICAgICAnd3JpdGluZyc6ICfinI3vuI8gQ29udGVudCBXcml0aW5nIEV4cGVydCcsXG4gICAgICAgICdjb2RpbmdfYmFja2VuZCc6ICfimpnvuI8gQmFja2VuZCBEZXZlbG9wbWVudCBFeHBlcnQnLFxuICAgICAgICAnY29kaW5nX2Zyb250ZW5kJzogJ/CfjqggRnJvbnRlbmQgRGV2ZWxvcG1lbnQgRXhwZXJ0JyxcbiAgICAgICAgJ2dlbmVyYWxfY2hhdCc6ICfwn6SWIEdlbmVyYWwgQUkgQXNzaXN0YW50J1xuICAgICAgfTtcbiAgICAgIFxuICAgICAgY29uc3QgYWdlbnROYW1lID0gcm9sZU5hbWVzW3JvbGUgYXMga2V5b2YgdHlwZW9mIHJvbGVOYW1lc10gfHwgJ/CfpJYgQUkgU3BlY2lhbGlzdCc7XG4gICAgICBcbiAgICAgIC8vIFVwZGF0ZSBjdXJyZW50IHByb2Nlc3Npbmcgc3RlcCB3aXRoIGFnZW50IGluZm9cbiAgICAgIHVwZGF0ZVN0ZXAoe1xuICAgICAgICB0aXRsZTogYCR7YWdlbnROYW1lfSBXb3JraW5nYCxcbiAgICAgICAgZGVzY3JpcHRpb246IHRhc2ssXG4gICAgICAgIGRldGFpbHM6IFtcbiAgICAgICAgICBg8J+OryBTcGVjaWFsaXN0OiAke2FnZW50TmFtZX1gLFxuICAgICAgICAgICfwn6egIEFwcGx5aW5nIGRlZXAgZXhwZXJ0aXNlIHRvIHlvdXIgcmVxdWVzdCcsXG4gICAgICAgICAgJ+KaoSBHZW5lcmF0aW5nIGhpZ2gtcXVhbGl0eSwgdGFpbG9yZWQgcmVzcG9uc2UnXG4gICAgICAgIF1cbiAgICAgIH0pO1xuICAgIH0sXG4gICAgXG4gICAgb25BZ2VudFdvcmtDb21wbGV0ZTogKHJvbGU6IHN0cmluZywgcmVzdWx0OiBzdHJpbmcpID0+IHtcbiAgICAgIHVwZGF0ZVN0ZXAoe1xuICAgICAgICBkZXNjcmlwdGlvbjogJ1NwZWNpYWxpc3Qgd29yayBjb21wbGV0ZWQsIHByZXBhcmluZyBmaW5hbCByZXNwb25zZS4uLicsXG4gICAgICAgIHByb2dyZXNzOiA4MFxuICAgICAgfSk7XG4gICAgfSxcbiAgICBcbiAgICBvblN1cGVydmlzb3JTeW50aGVzaXNTdGFydDogKCkgPT4ge1xuICAgICAgc3RhcnRTdGVwKE1JTklNQUxJU1RfU1RBVFVTX1NURVBTLlNZTlRIRVNJWklORyk7XG4gICAgfSxcbiAgICBcbiAgICBvblN1cGVydmlzb3JTeW50aGVzaXNDb21wbGV0ZTogKHN5bnRoZXNpczogc3RyaW5nKSA9PiB7XG4gICAgICBjb21wbGV0ZVN0ZXAoW1xuICAgICAgICAn4pyFIEZpbmFsIHJlc3BvbnNlIHN5bnRoZXNpcyBjb21wbGV0ZWQnLFxuICAgICAgICAn8J+OryBBbGwgc3BlY2lhbGlzdCBpbnNpZ2h0cyBzdWNjZXNzZnVsbHkgY29tYmluZWQnLFxuICAgICAgICAn8J+agCBZb3VyIHJlc3BvbnNlIGlzIHJlYWR5ISdcbiAgICAgIF0pO1xuICAgIH0sXG4gICAgXG4gICAgb25PcmNoZXN0cmF0aW9uQ29tcGxldGU6IChyZXN1bHQ6IGFueSkgPT4ge1xuICAgICAgLy8gRmluYWwgdmFsaWRhdGlvbiBzdGVwXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc3RhcnRTdGVwKE1JTklNQUxJU1RfU1RBVFVTX1NURVBTLlZBTElEQVRJTkcpO1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICBjb21wbGV0ZVN0ZXAoW1xuICAgICAgICAgICAgJ+KchSBRdWFsaXR5IHZhbGlkYXRpb24gY29tcGxldGVkJyxcbiAgICAgICAgICAgICfwn46vIFJlc3BvbnNlIG1lZXRzIGFsbCByZXF1aXJlbWVudHMnLFxuICAgICAgICAgICAgJ/CfmoAgT3JjaGVzdHJhdGlvbiBzdWNjZXNzZnVsISdcbiAgICAgICAgICBdKTtcbiAgICAgICAgICBjb21wbGV0ZVRyYWNraW5nKCk7XG4gICAgICAgIH0sIDE1MDApO1xuICAgICAgfSwgNTAwKTtcbiAgICB9LFxuICAgIFxuICAgIG9uRXJyb3I6IChzdGVwOiBzdHJpbmcsIGVycm9yOiBzdHJpbmcpID0+IHtcbiAgICAgIGVycm9yU3RlcChgRXJyb3IgaW4gJHtzdGVwfTogJHtlcnJvcn1gKTtcbiAgICB9XG4gIH0sIFtzdGFydFN0ZXAsIHVwZGF0ZVN0ZXAsIGNvbXBsZXRlU3RlcCwgZXJyb3JTdGVwLCBjb21wbGV0ZVRyYWNraW5nLCBjdXJyZW50U3RlcF0pO1xuXG4gIC8vIFByb3ZpZGUgcHJvZ3Jlc3MgY2FsbGJhY2sgdG8gcGFyZW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG9uUHJvZ3Jlc3NDYWxsYmFjaykge1xuICAgICAgb25Qcm9ncmVzc0NhbGxiYWNrKHByb2dyZXNzQ2FsbGJhY2spO1xuICAgIH1cbiAgfSwgW29uUHJvZ3Jlc3NDYWxsYmFjaywgcHJvZ3Jlc3NDYWxsYmFja10pO1xuXG4gIC8vIEVuaGFuY2VkIHN0YXJ0L3N0b3AgdHJhY2tpbmcgd2l0aCByZWFsaXN0aWMgZmxvd1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc0FjdGl2ZSAmJiAhc3RhdHVzQWN0aXZlKSB7XG4gICAgICBzdGFydFRyYWNraW5nKCk7XG5cbiAgICAgIC8vIFN0YXJ0IHdpdGggY29ubmVjdGlvbiBzdGVwXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc3RhcnRTdGVwKE1JTklNQUxJU1RfU1RBVFVTX1NURVBTLkNPTk5FQ1RJTkcpO1xuICAgICAgfSwgMzAwKTtcblxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGNvbXBsZXRlU3RlcChbJ/CflJcgU2VjdXJlIGNvbm5lY3Rpb25zIGVzdGFibGlzaGVkJywgJ+KaoSBSZWFkeSB0byBiZWdpbiBhbmFseXNpcyddKTtcbiAgICAgICAgXG4gICAgICAgIC8vIE1vdmUgdG8gYW5hbHlzaXNcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgc3RhcnRTdGVwKE1JTklNQUxJU1RfU1RBVFVTX1NURVBTLkFOQUxZWklORyk7XG4gICAgICAgIH0sIDgwMCk7XG5cbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgY29tcGxldGVTdGVwKFtcbiAgICAgICAgICAgICfwn46vIFJlcXVlc3QgYW5hbHlzaXMgY29tcGxldGVkJyxcbiAgICAgICAgICAgICfwn6egIE9wdGltYWwgYXBwcm9hY2ggaWRlbnRpZmllZCcsXG4gICAgICAgICAgICAn8J+agCBSZWFkeSB0byBwcm9jZXNzIHlvdXIgcmVxdWVzdCdcbiAgICAgICAgICBdKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBNb3ZlIHRvIHNwZWNpYWxpc3QgcHJlcGFyYXRpb25cbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIHN0YXJ0U3RlcChNSU5JTUFMSVNUX1NUQVRVU19TVEVQUy5QUkVQQVJJTkdfU1BFQ0lBTElTVCk7XG4gICAgICAgICAgfSwgODAwKTtcblxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgY29tcGxldGVTdGVwKFtcbiAgICAgICAgICAgICAgJ/CfpJYgQUkgc3BlY2lhbGlzdCByZWFkeSB0byB3b3JrJyxcbiAgICAgICAgICAgICAgJ+Kame+4jyBFeHBlcnQgY29uZmlndXJlZCB3aXRoIG9wdGltYWwgc2V0dGluZ3MnLFxuICAgICAgICAgICAgICAn4pqhIEJlZ2lubmluZyBwcm9jZXNzaW5nLi4uJ1xuICAgICAgICAgICAgXSk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIE1vdmUgdG8gcHJvY2Vzc2luZ1xuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIHN0YXJ0U3RlcChNSU5JTUFMSVNUX1NUQVRVU19TVEVQUy5QUk9DRVNTSU5HKTtcbiAgICAgICAgICAgIH0sIDgwMCk7XG4gICAgICAgICAgfSwgMzAwMCk7XG4gICAgICAgIH0sIDI1MDApO1xuICAgICAgfSwgMTUwMCk7XG5cbiAgICB9IGVsc2UgaWYgKCFpc0FjdGl2ZSAmJiBzdGF0dXNBY3RpdmUpIHtcbiAgICAgIC8vIERlbGF5IGJlZm9yZSByZXNldHRpbmcgdG8gc2hvdyBmaW5hbCBzdGF0ZVxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHJlc2V0VHJhY2tpbmcoKTtcbiAgICAgIH0sIDQwMDApO1xuICAgIH1cbiAgfSwgW2lzQWN0aXZlLCBzdGF0dXNBY3RpdmUsIHN0YXJ0VHJhY2tpbmcsIHJlc2V0VHJhY2tpbmcsIHN0YXJ0U3RlcCwgY29tcGxldGVTdGVwXSk7XG5cbiAgY29uc29sZS5sb2coJ/Cfjq8gTWluaW1hbGlzdCByZW5kZXIgZGVjaXNpb246JywgeyBzdGF0dXNBY3RpdmUsIGN1cnJlbnRTdGVwOiAhIWN1cnJlbnRTdGVwLCBzaG91bGRSZW5kZXI6IHN0YXR1c0FjdGl2ZSAmJiBjdXJyZW50U3RlcCB9KTtcblxuICBpZiAoIXN0YXR1c0FjdGl2ZSB8fCAhY3VycmVudFN0ZXApIHtcbiAgICBjb25zb2xlLmxvZygn8J+OryBOb3QgcmVuZGVyaW5nIG1pbmltYWxpc3QgLSBzdGF0dXNBY3RpdmU6Jywgc3RhdHVzQWN0aXZlLCAnY3VycmVudFN0ZXA6JywgISFjdXJyZW50U3RlcCk7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICBjb25zb2xlLmxvZygn8J+OryBSZW5kZXJpbmcgbWluaW1hbGlzdCBzdGF0dXM6JywgY3VycmVudFN0ZXAudGl0bGUpO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NsYXNzTmFtZX0+XG4gICAgICA8TWluaW1hbGlzdE9yY2hlc3RyYXRpb25TdGF0dXNcbiAgICAgICAgY3VycmVudFN0ZXA9e2N1cnJlbnRTdGVwfVxuICAgICAgICBpc0FjdGl2ZT17c3RhdHVzQWN0aXZlfVxuICAgICAgICBwcm9ncmVzcz17b3ZlcmFsbFByb2dyZXNzfVxuICAgICAgICBzaG93RGV0YWlscz17c2hvd0RldGFpbHN9XG4gICAgICAgIGFsbFN0ZXBzPXthbGxTdGVwc31cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIk1pbmltYWxpc3RPcmNoZXN0cmF0aW9uU3RhdHVzIiwidXNlTWluaW1hbGlzdE9yY2hlc3RyYXRpb25TdGF0dXMiLCJNSU5JTUFMSVNUX1NUQVRVU19TVEVQUyIsIk1pbmltYWxpc3RPcmNoZXN0cmF0aW9uQnJpZGdlIiwiaXNBY3RpdmUiLCJvblByb2dyZXNzQ2FsbGJhY2siLCJjbGFzc05hbWUiLCJzaG93RGV0YWlscyIsImNvbnNvbGUiLCJsb2ciLCJjdXJyZW50U3RlcCIsInN0YXR1c0FjdGl2ZSIsIm92ZXJhbGxQcm9ncmVzcyIsInN0YXJ0U3RlcCIsInVwZGF0ZVN0ZXAiLCJjb21wbGV0ZVN0ZXAiLCJlcnJvclN0ZXAiLCJ1cGRhdGVQcm9ncmVzcyIsInN0YXJ0VHJhY2tpbmciLCJjb21wbGV0ZVRyYWNraW5nIiwicmVzZXRUcmFja2luZyIsInRpdGxlIiwicHJvZ3Jlc3NDYWxsYmFjayIsIm9uQ2xhc3NpZmljYXRpb25TdGFydCIsIkNMQVNTSUZJQ0FUSU9OIiwib25DbGFzc2lmaWNhdGlvbkNvbXBsZXRlIiwicm9sZXMiLCJ0aHJlc2hvbGQiLCJsZW5ndGgiLCJNYXRoIiwicm91bmQiLCJzZXRUaW1lb3V0IiwiQ09PUkRJTkFUSU5HX1RFQU0iLCJQUkVQQVJJTkdfU1BFQ0lBTElTVCIsIm9uUm9sZVNlbGVjdGlvbkNvbXBsZXRlIiwic2VsZWN0ZWRSb2xlcyIsImZpbHRlcmVkUm9sZXMiLCJkZXNjcmlwdGlvbiIsImRldGFpbHMiLCJvbldvcmtmbG93U2VsZWN0aW9uQ29tcGxldGUiLCJ3b3JrZmxvd1R5cGUiLCJyZWFzb25pbmciLCJ3b3JrZmxvd05hbWVzIiwib25BZ2VudENyZWF0aW9uU3RhcnQiLCJvbkFnZW50Q3JlYXRpb25Db21wbGV0ZSIsImFnZW50cyIsIlBST0NFU1NJTkciLCJvblN1cGVydmlzb3JJbml0U3RhcnQiLCJvblN1cGVydmlzb3JJbml0Q29tcGxldGUiLCJzdXBlcnZpc29yUm9sZSIsIm9uVGFza1BsYW5uaW5nU3RhcnQiLCJwcm9ncmVzcyIsIm9uVGFza1BsYW5uaW5nQ29tcGxldGUiLCJwbGFuIiwib25BZ2VudFdvcmtTdGFydCIsInJvbGUiLCJ0YXNrIiwicm9sZU5hbWVzIiwiYWdlbnROYW1lIiwib25BZ2VudFdvcmtDb21wbGV0ZSIsInJlc3VsdCIsIm9uU3VwZXJ2aXNvclN5bnRoZXNpc1N0YXJ0IiwiU1lOVEhFU0laSU5HIiwib25TdXBlcnZpc29yU3ludGhlc2lzQ29tcGxldGUiLCJzeW50aGVzaXMiLCJvbk9yY2hlc3RyYXRpb25Db21wbGV0ZSIsIlZBTElEQVRJTkciLCJvbkVycm9yIiwic3RlcCIsImVycm9yIiwiQ09OTkVDVElORyIsIkFOQUxZWklORyIsInNob3VsZFJlbmRlciIsImRpdiIsImFsbFN0ZXBzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MinimalistOrchestrationBridge.tsx\n"));

/***/ })

});