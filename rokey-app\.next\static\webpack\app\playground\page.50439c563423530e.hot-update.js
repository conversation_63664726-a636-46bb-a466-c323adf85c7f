"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts":
/*!*******************************************************!*\
  !*** ./src/hooks/useMinimalistOrchestrationStatus.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MINIMALIST_STATUS_STEPS: () => (/* binding */ MINIMALIST_STATUS_STEPS),\n/* harmony export */   useMinimalistOrchestrationStatus: () => (/* binding */ useMinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMinimalistOrchestrationStatus,MINIMALIST_STATUS_STEPS auto */ \nfunction useMinimalistOrchestrationStatus() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [allSteps, setAllSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [overallProgress, setOverallProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const progressTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const stepCounterRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    // Start a new status step\n    const startStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (event)=>{\n            stepCounterRef.current += 1;\n            const newStep = {\n                id: \"step_\".concat(Date.now(), \"_\").concat(stepCounterRef.current),\n                title: event.title,\n                description: event.description,\n                status: 'in_progress',\n                icon: event.icon,\n                color: event.color,\n                progress: event.progress || 0,\n                details: event.details,\n                timestamp: new Date()\n            };\n            setCurrentStep(newStep);\n            setAllSteps({\n                \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (prev)=>[\n                        ...prev,\n                        newStep\n                    ]\n            }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"]);\n            setIsActive(true);\n            // Simulate progress if duration is provided\n            if (event.duration && event.duration > 0) {\n                simulateProgress(event.duration, event.progress || 0);\n            }\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"], []);\n    // Update current step\n    const updateStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (updates)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const updated = {\n                        ...prev,\n                        ...updates,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (step)=>step.id === prev.id ? updated : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n                    return updated;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"], []);\n    // Complete current step\n    const completeStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (finalDetails)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const completed = {\n                        ...prev,\n                        status: 'completed',\n                        progress: 100,\n                        details: finalDetails || prev.details,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (step)=>step.id === prev.id ? completed : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n                    return completed;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n            setOverallProgress(100);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"], []);\n    // Error in current step\n    const errorStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (error)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'error',\n                        description: error,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"], []);\n    // Update progress\n    const updateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (progress, description)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (prev)=>prev ? {\n                        ...prev,\n                        progress,\n                        ...description && {\n                            description\n                        },\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"]);\n            setOverallProgress(progress);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"], []);\n    // Simulate realistic progress over time\n    const simulateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": function(durationSeconds) {\n            let startProgress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n            }\n            const steps = Math.max(10, durationSeconds * 2); // Update every 500ms for smooth animation\n            const progressIncrement = (100 - startProgress) / steps;\n            let currentProgress = startProgress;\n            let stepCount = 0;\n            progressTimerRef.current = setInterval({\n                \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": ()=>{\n                    stepCount++;\n                    // Use easing function for more natural progress\n                    const easedProgress = startProgress + (100 - startProgress) * easeOutCubic(stepCount / steps);\n                    currentProgress = Math.min(easedProgress, 95); // Cap at 95% until manually completed\n                    updateProgress(currentProgress);\n                    if (stepCount >= steps) {\n                        if (progressTimerRef.current) {\n                            clearInterval(progressTimerRef.current);\n                            progressTimerRef.current = null;\n                        }\n                    }\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], 500);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], [\n        updateProgress\n    ]);\n    // Start orchestration tracking\n    const startTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startTracking]\": ()=>{\n            setIsActive(true);\n            setOverallProgress(0);\n            setCurrentStep(null);\n            setAllSteps([]);\n            stepCounterRef.current = 0;\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startTracking]\"], []);\n    // Complete orchestration tracking\n    const completeTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setOverallProgress(100);\n            // Keep active for a bit to show completion state\n            setTimeout({\n                \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n                    setIsActive(false);\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], 3000);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], []);\n    // Reset tracking\n    const resetTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[resetTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setCurrentStep(null);\n            setAllSteps([]);\n            setIsActive(false);\n            setOverallProgress(0);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[resetTracking]\"], []);\n    return {\n        currentStep,\n        allSteps,\n        isActive,\n        overallProgress,\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        updateProgress,\n        startTracking,\n        completeTracking,\n        resetTracking\n    };\n}\n// Easing function for smooth progress animation\nfunction easeOutCubic(t) {\n    return 1 - Math.pow(1 - t, 3);\n}\n// Predefined status steps for common orchestration phases\nconst MINIMALIST_STATUS_STEPS = {\n    // Connection and initialization\n    CONNECTING: {\n        type: 'connecting',\n        title: '🔗 Establishing Connections',\n        description: 'Setting up secure connections to AI providers...',\n        icon: 'connecting',\n        color: 'orange',\n        duration: 2\n    },\n    // Analysis phase\n    ANALYZING: {\n        type: 'analyzing',\n        title: '🔍 Analyzing Your Request',\n        description: 'RouKey AI is examining your request to understand the best approach...',\n        icon: 'analysis',\n        color: 'blue',\n        duration: 3,\n        details: [\n            'Understanding request complexity and requirements',\n            'Identifying the most suitable AI approach',\n            'Preparing optimal processing strategy'\n        ]\n    },\n    // Classification\n    CLASSIFICATION: {\n        type: 'classification',\n        title: '🧠 Understanding Your Needs',\n        description: 'Determining the perfect AI specialist for your request...',\n        icon: 'classification',\n        color: 'purple',\n        duration: 3,\n        details: [\n            'Analyzing request type and complexity',\n            'Matching requirements to specialist expertise',\n            'Selecting optimal AI configuration'\n        ]\n    },\n    // Agent preparation\n    PREPARING_SPECIALIST: {\n        type: 'agent_creation',\n        title: '🤖 Preparing AI Specialist',\n        description: 'Setting up the perfect expert for your task...',\n        icon: 'agents',\n        color: 'teal',\n        duration: 4,\n        details: [\n            'Configuring AI specialist with optimal parameters',\n            'Loading relevant knowledge and capabilities',\n            'Establishing secure processing environment'\n        ]\n    },\n    // Multi-agent coordination\n    COORDINATING_TEAM: {\n        type: 'supervisor_init',\n        title: '✨ Coordinating AI Team',\n        description: 'Setting up collaborative workflow between specialists...',\n        icon: 'supervisor',\n        color: 'orange',\n        duration: 2,\n        details: [\n            'Initializing team coordination protocols',\n            'Establishing communication channels',\n            'Preparing collaborative workspace'\n        ]\n    },\n    // Active processing\n    PROCESSING: {\n        type: 'agent_working',\n        title: '⚙️ AI Specialist Working',\n        description: 'Your dedicated expert is crafting the perfect response...',\n        icon: 'working',\n        color: 'cyan',\n        duration: 15,\n        details: [\n            'Applying specialized expertise to your request',\n            'Generating high-quality, tailored content',\n            'Ensuring accuracy and relevance'\n        ]\n    },\n    // Synthesis phase\n    SYNTHESIZING: {\n        type: 'supervisor_synthesis',\n        title: '🧪 Finalizing Response',\n        description: 'Combining all insights into your perfect response...',\n        icon: 'synthesis',\n        color: 'emerald',\n        duration: 5,\n        details: [\n            'Reviewing and combining all contributions',\n            'Ensuring coherence and quality',\n            'Applying final optimizations'\n        ]\n    },\n    // Simple thinking for single-agent\n    THINKING: {\n        type: 'thinking',\n        title: '🧠 AI Thinking',\n        description: 'Processing your request and formulating the best response...',\n        icon: 'thinking',\n        color: 'purple',\n        duration: 8,\n        details: [\n            'Analyzing your request in detail',\n            'Accessing relevant knowledge and context',\n            'Crafting personalized, high-quality response'\n        ]\n    },\n    // Generation phase\n    GENERATING: {\n        type: 'generating',\n        title: '✍️ Generating Response',\n        description: 'Creating your personalized response...',\n        icon: 'generating',\n        color: 'rose',\n        duration: 10,\n        details: [\n            'Applying AI expertise to your specific needs',\n            'Generating detailed, accurate content',\n            'Ensuring quality and relevance'\n        ]\n    },\n    // Optimization\n    OPTIMIZING: {\n        type: 'optimizing',\n        title: '⚡ Optimizing Results',\n        description: 'Fine-tuning for the best possible outcome...',\n        icon: 'optimizing',\n        color: 'green',\n        duration: 3,\n        details: [\n            'Reviewing generated content for quality',\n            'Applying final optimizations',\n            'Ensuring perfect alignment with your needs'\n        ]\n    },\n    // Validation\n    VALIDATING: {\n        type: 'validating',\n        title: '✅ Quality Check',\n        description: 'Ensuring accuracy and quality of the response...',\n        icon: 'validating',\n        color: 'indigo',\n        duration: 2,\n        details: [\n            'Performing comprehensive quality review',\n            'Validating accuracy and completeness',\n            'Confirming optimal response quality'\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\n"));

/***/ })

});