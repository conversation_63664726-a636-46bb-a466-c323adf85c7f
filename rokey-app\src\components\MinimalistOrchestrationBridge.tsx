'use client';

import React, { useEffect, useCallback } from 'react';
import MinimalistOrchestrationStatus from './MinimalistOrchestrationStatus';
import { useMinimalistOrchestrationStatus, MINIMALIST_STATUS_STEPS } from '@/hooks/useMinimalistOrchestrationStatus';
import { type ProgressCallback } from '@/lib/langgraph-orchestration/RouKeyLangGraphIntegration';

interface MinimalistOrchestrationBridgeProps {
  isActive: boolean;
  onProgressCallback?: (callback: ProgressCallback) => void;
  className?: string;
  showDetails?: boolean;
}

export default function MinimalistOrchestrationBridge({
  isActive,
  onProgressCallback,
  className = '',
  showDetails = true
}: MinimalistOrchestrationBridgeProps) {
  console.log('🎯 MinimalistOrchestrationBridge render:', { isActive, className });

  const {
    currentStep,
    allSteps,
    isActive: statusActive,
    overallProgress,
    startStep,
    updateStep,
    completeStep,
    errorStep,
    updateProgress,
    startTracking,
    completeTracking,
    resetTracking
  } = useMinimalistOrchestrationStatus();

  console.log('🎯 Minimalist status state:', { currentStep: currentStep?.title, statusActive, overallProgress });

  // Create progress callback for orchestration system
  const progressCallback: ProgressCallback = useCallback({
    onClassificationStart: () => {
      startStep(MINIMALIST_STATUS_STEPS.CLASSIFICATION);
    },
    
    onClassificationComplete: (roles: string[], threshold: number) => {
      completeStep([
        `✨ Identified ${roles.length} specialist areas needed`,
        `🎯 Analysis completed with ${Math.round(threshold * 100)}% confidence`,
        `🚀 Ready to assemble your AI dream team`
      ]);
      
      // Transition to next step
      setTimeout(() => {
        if (roles.length > 1) {
          startStep(MINIMALIST_STATUS_STEPS.COORDINATING_TEAM);
        } else {
          startStep(MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);
        }
      }, 1000);
    },
    
    onRoleSelectionComplete: (selectedRoles: string[], filteredRoles: string[]) => {
      updateStep({
        description: `Selected ${selectedRoles.length} AI specialists for optimal collaboration`,
        details: [
          `🎯 ${selectedRoles.length} specialists selected`,
          `⚡ Each expert optimized for your specific needs`,
          `🤝 Team ready for collaborative processing`
        ]
      });
    },
    
    onWorkflowSelectionComplete: (workflowType: string, reasoning: string) => {
      const workflowNames = {
        'sequential': 'step-by-step collaboration',
        'supervisor': 'coordinated teamwork',
        'hierarchical': 'multi-level coordination',
        'parallel': 'simultaneous processing'
      };
      
      updateStep({
        description: `Configured for ${workflowNames[workflowType as keyof typeof workflowNames] || 'smart collaboration'}`,
        details: [
          `🎯 Strategy: ${workflowNames[workflowType as keyof typeof workflowNames] || workflowType}`,
          `🧠 Optimized for your specific request type`,
          `⚡ Team coordination plan established`
        ]
      });
    },
    
    onAgentCreationStart: () => {
      if (currentStep?.title !== '🤖 Preparing AI Specialist') {
        startStep(MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);
      }
    },
    
    onAgentCreationComplete: (agents: Array<{ role: string, apiKey: string }>) => {
      completeStep([
        `🤖 ${agents.length} AI specialist${agents.length > 1 ? 's' : ''} ready to work`,
        `⚙️ Expert${agents.length > 1 ? 's' : ''} configured with optimal settings`,
        `🚀 Ready to begin processing your request`
      ]);
      
      // Transition to processing
      setTimeout(() => {
        startStep(MINIMALIST_STATUS_STEPS.PROCESSING);
      }, 1000);
    },
    
    onSupervisorInitStart: () => {
      if (currentStep?.title !== '✨ Coordinating AI Team') {
        startStep(MINIMALIST_STATUS_STEPS.COORDINATING_TEAM);
      }
    },
    
    onSupervisorInitComplete: (supervisorRole: string) => {
      completeStep([
        '✨ Team coordinator activated',
        '📡 Communication channels established',
        '🎯 Ready for collaborative processing'
      ]);
    },
    
    onTaskPlanningStart: () => {
      updateStep({
        description: 'Breaking down your request into specialized tasks...',
        progress: 20
      });
    },
    
    onTaskPlanningComplete: (plan: string) => {
      updateStep({
        description: 'Task planning completed, beginning specialist work...',
        progress: 40
      });
    },
    
    onAgentWorkStart: (role: string, task: string) => {
      const roleNames = {
        'brainstorming_ideation': '💡 Creative Ideation Specialist',
        'writing': '✍️ Content Writing Expert',
        'coding_backend': '⚙️ Backend Development Expert',
        'coding_frontend': '🎨 Frontend Development Expert',
        'general_chat': '🤖 General AI Assistant'
      };
      
      const agentName = roleNames[role as keyof typeof roleNames] || '🤖 AI Specialist';
      
      // Update current processing step with agent info
      updateStep({
        title: `${agentName} Working`,
        description: task,
        details: [
          `🎯 Specialist: ${agentName}`,
          '🧠 Applying deep expertise to your request',
          '⚡ Generating high-quality, tailored response'
        ]
      });
    },
    
    onAgentWorkComplete: (role: string, result: string) => {
      updateStep({
        description: 'Specialist work completed, preparing final response...',
        progress: 80
      });
    },
    
    onSupervisorSynthesisStart: () => {
      startStep(MINIMALIST_STATUS_STEPS.SYNTHESIZING);
    },
    
    onSupervisorSynthesisComplete: (synthesis: string) => {
      completeStep([
        '✅ Final response synthesis completed',
        '🎯 All specialist insights successfully combined',
        '🚀 Your response is ready!'
      ]);
    },
    
    onOrchestrationComplete: (result: any) => {
      // Final validation step
      setTimeout(() => {
        startStep(MINIMALIST_STATUS_STEPS.VALIDATING);
        setTimeout(() => {
          completeStep([
            '✅ Quality validation completed',
            '🎯 Response meets all requirements',
            '🚀 Orchestration successful!'
          ]);
          completeTracking();
        }, 1500);
      }, 500);
    },
    
    onError: (step: string, error: string) => {
      errorStep(`Error in ${step}: ${error}`);
    }
  }, [startStep, updateStep, completeStep, errorStep, completeTracking, currentStep]);

  // Provide progress callback to parent
  useEffect(() => {
    if (onProgressCallback) {
      onProgressCallback(progressCallback);
    }
  }, [onProgressCallback, progressCallback]);

  // Enhanced start/stop tracking with realistic flow
  useEffect(() => {
    if (isActive && !statusActive) {
      startTracking();

      // Start with connection step
      setTimeout(() => {
        startStep(MINIMALIST_STATUS_STEPS.CONNECTING);
      }, 300);

      setTimeout(() => {
        completeStep(['🔗 Secure connections established', '⚡ Ready to begin analysis']);
        
        // Move to analysis
        setTimeout(() => {
          startStep(MINIMALIST_STATUS_STEPS.ANALYZING);
        }, 800);

        setTimeout(() => {
          completeStep([
            '🎯 Request analysis completed',
            '🧠 Optimal approach identified',
            '🚀 Ready to process your request'
          ]);
          
          // Move to specialist preparation
          setTimeout(() => {
            startStep(MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);
          }, 800);

          setTimeout(() => {
            completeStep([
              '🤖 AI specialist ready to work',
              '⚙️ Expert configured with optimal settings',
              '⚡ Beginning processing...'
            ]);
            
            // Move to processing
            setTimeout(() => {
              startStep(MINIMALIST_STATUS_STEPS.PROCESSING);
            }, 800);
          }, 3000);
        }, 2500);
      }, 1500);

    } else if (!isActive && statusActive) {
      // Delay before resetting to show final state
      setTimeout(() => {
        resetTracking();
      }, 4000);
    }
  }, [isActive, statusActive, startTracking, resetTracking, startStep, completeStep]);

  console.log('🎯 Minimalist render decision:', { statusActive, currentStep: !!currentStep, shouldRender: statusActive && currentStep });

  if (!statusActive || !currentStep) {
    console.log('🎯 Not rendering minimalist - statusActive:', statusActive, 'currentStep:', !!currentStep);
    return null;
  }

  console.log('🎯 Rendering minimalist status:', currentStep.title);

  return (
    <div className={className}>
      <MinimalistOrchestrationStatus
        currentStep={currentStep}
        isActive={statusActive}
        progress={overallProgress}
        showDetails={showDetails}
        allSteps={allSteps}
      />
    </div>
  );
}
