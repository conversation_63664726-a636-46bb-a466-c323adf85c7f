"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts":
/*!*******************************************************!*\
  !*** ./src/hooks/useMinimalistOrchestrationStatus.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MINIMALIST_STATUS_STEPS: () => (/* binding */ MINIMALIST_STATUS_STEPS),\n/* harmony export */   useMinimalistOrchestrationStatus: () => (/* binding */ useMinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMinimalistOrchestrationStatus,MINIMALIST_STATUS_STEPS auto */ \nfunction useMinimalistOrchestrationStatus() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [allSteps, setAllSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [overallProgress, setOverallProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const progressTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Start a new status step\n    const startStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (event)=>{\n            const newStep = {\n                id: \"step_\".concat(Date.now()),\n                title: event.title,\n                description: event.description,\n                status: 'in_progress',\n                icon: event.icon,\n                color: event.color,\n                progress: event.progress || 0,\n                details: event.details,\n                timestamp: new Date()\n            };\n            setCurrentStep(newStep);\n            setAllSteps({\n                \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (prev)=>[\n                        ...prev,\n                        newStep\n                    ]\n            }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"]);\n            setIsActive(true);\n            // Simulate progress if duration is provided\n            if (event.duration && event.duration > 0) {\n                simulateProgress(event.duration, event.progress || 0);\n            }\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"], []);\n    // Update current step\n    const updateStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (updates)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const updated = {\n                        ...prev,\n                        ...updates,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (step)=>step.id === prev.id ? updated : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n                    return updated;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"], []);\n    // Complete current step\n    const completeStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (finalDetails)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const completed = {\n                        ...prev,\n                        status: 'completed',\n                        progress: 100,\n                        details: finalDetails || prev.details,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (step)=>step.id === prev.id ? completed : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n                    return completed;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n            setOverallProgress(100);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"], []);\n    // Error in current step\n    const errorStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (error)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'error',\n                        description: error,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"], []);\n    // Update progress\n    const updateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (progress, description)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (prev)=>prev ? {\n                        ...prev,\n                        progress,\n                        ...description && {\n                            description\n                        },\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"]);\n            setOverallProgress(progress);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"], []);\n    // Simulate realistic progress over time\n    const simulateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": function(durationSeconds) {\n            let startProgress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n            }\n            const steps = Math.max(10, durationSeconds * 2); // Update every 500ms for smooth animation\n            const progressIncrement = (100 - startProgress) / steps;\n            let currentProgress = startProgress;\n            let stepCount = 0;\n            progressTimerRef.current = setInterval({\n                \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": ()=>{\n                    stepCount++;\n                    // Use easing function for more natural progress\n                    const easedProgress = startProgress + (100 - startProgress) * easeOutCubic(stepCount / steps);\n                    currentProgress = Math.min(easedProgress, 95); // Cap at 95% until manually completed\n                    updateProgress(currentProgress);\n                    if (stepCount >= steps) {\n                        if (progressTimerRef.current) {\n                            clearInterval(progressTimerRef.current);\n                            progressTimerRef.current = null;\n                        }\n                    }\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], 500);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], [\n        updateProgress\n    ]);\n    // Start orchestration tracking\n    const startTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startTracking]\": ()=>{\n            setIsActive(true);\n            setOverallProgress(0);\n            setCurrentStep(null);\n            setAllSteps([]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startTracking]\"], []);\n    // Complete orchestration tracking\n    const completeTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setOverallProgress(100);\n            // Keep active for a bit to show completion state\n            setTimeout({\n                \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n                    setIsActive(false);\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], 3000);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], []);\n    // Reset tracking\n    const resetTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[resetTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setCurrentStep(null);\n            setIsActive(false);\n            setOverallProgress(0);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[resetTracking]\"], []);\n    return {\n        currentStep,\n        isActive,\n        overallProgress,\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        updateProgress,\n        startTracking,\n        completeTracking,\n        resetTracking\n    };\n}\n// Easing function for smooth progress animation\nfunction easeOutCubic(t) {\n    return 1 - Math.pow(1 - t, 3);\n}\n// Predefined status steps for common orchestration phases\nconst MINIMALIST_STATUS_STEPS = {\n    // Connection and initialization\n    CONNECTING: {\n        type: 'connecting',\n        title: '🔗 Establishing Connections',\n        description: 'Setting up secure connections to AI providers...',\n        icon: 'connecting',\n        color: 'orange',\n        duration: 2\n    },\n    // Analysis phase\n    ANALYZING: {\n        type: 'analyzing',\n        title: '🔍 Analyzing Your Request',\n        description: 'RouKey AI is examining your request to understand the best approach...',\n        icon: 'analysis',\n        color: 'blue',\n        duration: 3,\n        details: [\n            'Understanding request complexity and requirements',\n            'Identifying the most suitable AI approach',\n            'Preparing optimal processing strategy'\n        ]\n    },\n    // Classification\n    CLASSIFICATION: {\n        type: 'classification',\n        title: '🧠 Understanding Your Needs',\n        description: 'Determining the perfect AI specialist for your request...',\n        icon: 'classification',\n        color: 'purple',\n        duration: 3,\n        details: [\n            'Analyzing request type and complexity',\n            'Matching requirements to specialist expertise',\n            'Selecting optimal AI configuration'\n        ]\n    },\n    // Agent preparation\n    PREPARING_SPECIALIST: {\n        type: 'agent_creation',\n        title: '🤖 Preparing AI Specialist',\n        description: 'Setting up the perfect expert for your task...',\n        icon: 'agents',\n        color: 'teal',\n        duration: 4,\n        details: [\n            'Configuring AI specialist with optimal parameters',\n            'Loading relevant knowledge and capabilities',\n            'Establishing secure processing environment'\n        ]\n    },\n    // Multi-agent coordination\n    COORDINATING_TEAM: {\n        type: 'supervisor_init',\n        title: '✨ Coordinating AI Team',\n        description: 'Setting up collaborative workflow between specialists...',\n        icon: 'supervisor',\n        color: 'orange',\n        duration: 2,\n        details: [\n            'Initializing team coordination protocols',\n            'Establishing communication channels',\n            'Preparing collaborative workspace'\n        ]\n    },\n    // Active processing\n    PROCESSING: {\n        type: 'agent_working',\n        title: '⚙️ AI Specialist Working',\n        description: 'Your dedicated expert is crafting the perfect response...',\n        icon: 'working',\n        color: 'cyan',\n        duration: 15,\n        details: [\n            'Applying specialized expertise to your request',\n            'Generating high-quality, tailored content',\n            'Ensuring accuracy and relevance'\n        ]\n    },\n    // Synthesis phase\n    SYNTHESIZING: {\n        type: 'supervisor_synthesis',\n        title: '🧪 Finalizing Response',\n        description: 'Combining all insights into your perfect response...',\n        icon: 'synthesis',\n        color: 'emerald',\n        duration: 5,\n        details: [\n            'Reviewing and combining all contributions',\n            'Ensuring coherence and quality',\n            'Applying final optimizations'\n        ]\n    },\n    // Simple thinking for single-agent\n    THINKING: {\n        type: 'thinking',\n        title: '🧠 AI Thinking',\n        description: 'Processing your request and formulating the best response...',\n        icon: 'thinking',\n        color: 'purple',\n        duration: 8,\n        details: [\n            'Analyzing your request in detail',\n            'Accessing relevant knowledge and context',\n            'Crafting personalized, high-quality response'\n        ]\n    },\n    // Generation phase\n    GENERATING: {\n        type: 'generating',\n        title: '✍️ Generating Response',\n        description: 'Creating your personalized response...',\n        icon: 'generating',\n        color: 'rose',\n        duration: 10,\n        details: [\n            'Applying AI expertise to your specific needs',\n            'Generating detailed, accurate content',\n            'Ensuring quality and relevance'\n        ]\n    },\n    // Optimization\n    OPTIMIZING: {\n        type: 'optimizing',\n        title: '⚡ Optimizing Results',\n        description: 'Fine-tuning for the best possible outcome...',\n        icon: 'optimizing',\n        color: 'green',\n        duration: 3,\n        details: [\n            'Reviewing generated content for quality',\n            'Applying final optimizations',\n            'Ensuring perfect alignment with your needs'\n        ]\n    },\n    // Validation\n    VALIDATING: {\n        type: 'validating',\n        title: '✅ Quality Check',\n        description: 'Ensuring accuracy and quality of the response...',\n        icon: 'validating',\n        color: 'indigo',\n        duration: 2,\n        details: [\n            'Performing comprehensive quality review',\n            'Validating accuracy and completeness',\n            'Confirming optimal response quality'\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\n"));

/***/ })

});