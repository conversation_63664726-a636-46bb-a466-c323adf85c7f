"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/MinimalistOrchestrationBridge.tsx":
/*!**********************************************************!*\
  !*** ./src/components/MinimalistOrchestrationBridge.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MinimalistOrchestrationBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MinimalistOrchestrationStatus */ \"(app-pages-browser)/./src/components/MinimalistOrchestrationStatus.tsx\");\n/* harmony import */ var _hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useMinimalistOrchestrationStatus */ \"(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MinimalistOrchestrationBridge(param) {\n    let { isActive, onProgressCallback, className = '', showDetails = true } = param;\n    _s();\n    console.log('🎯 MinimalistOrchestrationBridge render:', {\n        isActive,\n        className\n    });\n    const { currentStep, allSteps, isActive: statusActive, overallProgress, startStep, updateStep, completeStep, errorStep, updateProgress, startTracking, completeTracking, resetTracking } = (0,_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.useMinimalistOrchestrationStatus)();\n    console.log('🎯 Minimalist status state:', {\n        currentStep: currentStep === null || currentStep === void 0 ? void 0 : currentStep.title,\n        statusActive,\n        overallProgress\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.CLASSIFICATION);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep([\n                    \"✨ Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83C\\uDFAF Analysis completed with \".concat(Math.round(threshold * 100), \"% confidence\"),\n                    \"\\uD83D\\uDE80 Ready to assemble your AI dream team\"\n                ]);\n                // Transition to next step\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                        if (roles.length > 1) {\n                            startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.COORDINATING_TEAM);\n                        } else {\n                            startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);\n                        }\n                    }\n                }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"], 1000);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                updateStep({\n                    description: \"Selected \".concat(selectedRoles.length, \" AI specialists for optimal collaboration\"),\n                    details: [\n                        \"\\uD83C\\uDFAF \".concat(selectedRoles.length, \" specialists selected\"),\n                        \"⚡ Each expert optimized for your specific needs\",\n                        \"\\uD83E\\uDD1D Team ready for collaborative processing\"\n                    ]\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                const workflowNames = {\n                    'sequential': 'step-by-step collaboration',\n                    'supervisor': 'coordinated teamwork',\n                    'hierarchical': 'multi-level coordination',\n                    'parallel': 'simultaneous processing'\n                };\n                updateStep({\n                    description: \"Configured for \".concat(workflowNames[workflowType] || 'smart collaboration'),\n                    details: [\n                        \"\\uD83C\\uDFAF Strategy: \".concat(workflowNames[workflowType] || workflowType),\n                        \"\\uD83E\\uDDE0 Optimized for your specific request type\",\n                        \"⚡ Team coordination plan established\"\n                    ]\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                if ((currentStep === null || currentStep === void 0 ? void 0 : currentStep.title) !== '🤖 Preparing AI Specialist') {\n                    startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);\n                }\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep([\n                    \"\\uD83E\\uDD16 \".concat(agents.length, \" AI specialist\").concat(agents.length > 1 ? 's' : '', \" ready to work\"),\n                    \"⚙️ Expert\".concat(agents.length > 1 ? 's' : '', \" configured with optimal settings\"),\n                    \"\\uD83D\\uDE80 Ready to begin processing your request\"\n                ]);\n                // Transition to processing\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                        startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PROCESSING);\n                    }\n                }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"], 1000);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                if ((currentStep === null || currentStep === void 0 ? void 0 : currentStep.title) !== '✨ Coordinating AI Team') {\n                    startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.COORDINATING_TEAM);\n                }\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep([\n                    '✨ Team coordinator activated',\n                    '📡 Communication channels established',\n                    '🎯 Ready for collaborative processing'\n                ]);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                updateStep({\n                    description: 'Breaking down your request into specialized tasks...',\n                    progress: 20\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (plan)=>{\n                updateStep({\n                    description: 'Task planning completed, beginning specialist work...',\n                    progress: 40\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': '💡 Creative Ideation Specialist',\n                    'writing': '✍️ Content Writing Expert',\n                    'coding_backend': '⚙️ Backend Development Expert',\n                    'coding_frontend': '🎨 Frontend Development Expert',\n                    'general_chat': '🤖 General AI Assistant'\n                };\n                const agentName = roleNames[role] || '🤖 AI Specialist';\n                // Update current processing step with agent info\n                updateStep({\n                    title: \"\".concat(agentName, \" Working\"),\n                    description: task,\n                    details: [\n                        \"\\uD83C\\uDFAF Specialist: \".concat(agentName),\n                        '🧠 Applying deep expertise to your request',\n                        '⚡ Generating high-quality, tailored response'\n                    ]\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (role, result)=>{\n                updateStep({\n                    description: 'Specialist work completed, preparing final response...',\n                    progress: 80\n                });\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.SYNTHESIZING);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep([\n                    '✅ Final response synthesis completed',\n                    '🎯 All specialist insights successfully combined',\n                    '🚀 Your response is ready!'\n                ]);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (result)=>{\n                // Final validation step\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                        startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.VALIDATING);\n                        setTimeout({\n                            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": ()=>{\n                                completeStep([\n                                    '✅ Quality validation completed',\n                                    '🎯 Response meets all requirements',\n                                    '🚀 Orchestration successful!'\n                                ]);\n                                completeTracking();\n                            }\n                        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"], 1500);\n                    }\n                }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"], 500);\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"MinimalistOrchestrationBridge.useCallback[progressCallback]\": (step, error)=>{\n                errorStep(\"Error in \".concat(step, \": \").concat(error));\n            }\n        }[\"MinimalistOrchestrationBridge.useCallback[progressCallback]\"]\n    }, [\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        completeTracking,\n        currentStep\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"MinimalistOrchestrationBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Enhanced start/stop tracking with realistic flow\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n            if (isActive && !statusActive) {\n                startTracking();\n                // Start with connection step\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                        startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.CONNECTING);\n                    }\n                }[\"MinimalistOrchestrationBridge.useEffect\"], 300);\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                        completeStep([\n                            '🔗 Secure connections established',\n                            '⚡ Ready to begin analysis'\n                        ]);\n                        // Move to analysis\n                        setTimeout({\n                            \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.ANALYZING);\n                            }\n                        }[\"MinimalistOrchestrationBridge.useEffect\"], 800);\n                        setTimeout({\n                            \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                completeStep([\n                                    '🎯 Request analysis completed',\n                                    '🧠 Optimal approach identified',\n                                    '🚀 Ready to process your request'\n                                ]);\n                                // Move to specialist preparation\n                                setTimeout({\n                                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                        startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PREPARING_SPECIALIST);\n                                    }\n                                }[\"MinimalistOrchestrationBridge.useEffect\"], 800);\n                                setTimeout({\n                                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                        completeStep([\n                                            '🤖 AI specialist ready to work',\n                                            '⚙️ Expert configured with optimal settings',\n                                            '⚡ Beginning processing...'\n                                        ]);\n                                        // Move to processing\n                                        setTimeout({\n                                            \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                                                startStep(_hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.MINIMALIST_STATUS_STEPS.PROCESSING);\n                                            }\n                                        }[\"MinimalistOrchestrationBridge.useEffect\"], 800);\n                                    }\n                                }[\"MinimalistOrchestrationBridge.useEffect\"], 3000);\n                            }\n                        }[\"MinimalistOrchestrationBridge.useEffect\"], 2500);\n                    }\n                }[\"MinimalistOrchestrationBridge.useEffect\"], 1500);\n            } else if (!isActive && statusActive) {\n                // Delay before resetting to show final state\n                setTimeout({\n                    \"MinimalistOrchestrationBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"MinimalistOrchestrationBridge.useEffect\"], 4000);\n            }\n        }\n    }[\"MinimalistOrchestrationBridge.useEffect\"], [\n        isActive,\n        statusActive,\n        startTracking,\n        resetTracking,\n        startStep,\n        completeStep\n    ]);\n    console.log('🎯 Minimalist render decision:', {\n        statusActive,\n        currentStep: !!currentStep,\n        shouldRender: statusActive && currentStep\n    });\n    if (!statusActive || !currentStep) {\n        console.log('🎯 Not rendering minimalist - statusActive:', statusActive, 'currentStep:', !!currentStep);\n        return null;\n    }\n    console.log('🎯 Rendering minimalist status:', currentStep.title);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            currentStep: currentStep,\n            isActive: statusActive,\n            progress: overallProgress,\n            showDetails: showDetails,\n            allSteps: allSteps\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationBridge.tsx\",\n            lineNumber: 272,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MinimalistOrchestrationBridge.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n_s(MinimalistOrchestrationBridge, \"VvyQjHErsQ0MdMClKwgLG31pOXs=\", false, function() {\n    return [\n        _hooks_useMinimalistOrchestrationStatus__WEBPACK_IMPORTED_MODULE_3__.useMinimalistOrchestrationStatus\n    ];\n});\n_c = MinimalistOrchestrationBridge;\nvar _c;\n$RefreshReg$(_c, \"MinimalistOrchestrationBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MinimalistOrchestrationBridge.tsx\n"));

/***/ })

});