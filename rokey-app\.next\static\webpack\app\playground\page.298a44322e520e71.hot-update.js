"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts":
/*!*******************************************************!*\
  !*** ./src/hooks/useMinimalistOrchestrationStatus.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MINIMALIST_STATUS_STEPS: () => (/* binding */ MINIMALIST_STATUS_STEPS),\n/* harmony export */   useMinimalistOrchestrationStatus: () => (/* binding */ useMinimalistOrchestrationStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMinimalistOrchestrationStatus,MINIMALIST_STATUS_STEPS auto */ \nfunction useMinimalistOrchestrationStatus() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [allSteps, setAllSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [overallProgress, setOverallProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const progressTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const stepCounterRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    // Start a new status step\n    const startStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (event)=>{\n            stepCounterRef.current += 1;\n            const newStep = {\n                id: \"step_\".concat(Date.now(), \"_\").concat(stepCounterRef.current),\n                title: event.title,\n                description: event.description,\n                status: 'in_progress',\n                icon: event.icon,\n                color: event.color,\n                progress: event.progress || 0,\n                details: event.details,\n                timestamp: new Date()\n            };\n            setCurrentStep(newStep);\n            setAllSteps({\n                \"useMinimalistOrchestrationStatus.useCallback[startStep]\": (prev)=>[\n                        ...prev,\n                        newStep\n                    ]\n            }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"]);\n            setIsActive(true);\n            // Simulate progress if duration is provided\n            if (event.duration && event.duration > 0) {\n                simulateProgress(event.duration, event.progress || 0);\n            }\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startStep]\"], []);\n    // Update current step\n    const updateStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (updates)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const updated = {\n                        ...prev,\n                        ...updates,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[updateStep]\": (step)=>step.id === prev.id ? updated : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n                    return updated;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateStep]\"], []);\n    // Complete current step\n    const completeStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (finalDetails)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prev)=>{\n                    if (!prev) return null;\n                    const completed = {\n                        ...prev,\n                        status: 'completed',\n                        progress: 100,\n                        details: finalDetails || prev.details,\n                        timestamp: new Date()\n                    };\n                    // Update in allSteps as well\n                    setAllSteps({\n                        \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (prevSteps)=>prevSteps.map({\n                                \"useMinimalistOrchestrationStatus.useCallback[completeStep]\": (step)=>step.id === prev.id ? completed : step\n                            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"])\n                    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n                    return completed;\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"]);\n            setOverallProgress(100);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeStep]\"], []);\n    // Error in current step\n    const errorStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (error)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[errorStep]\": (prev)=>prev ? {\n                        ...prev,\n                        status: 'error',\n                        description: error,\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[errorStep]\"], []);\n    // Update progress\n    const updateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (progress, description)=>{\n            setCurrentStep({\n                \"useMinimalistOrchestrationStatus.useCallback[updateProgress]\": (prev)=>prev ? {\n                        ...prev,\n                        progress,\n                        ...description && {\n                            description\n                        },\n                        timestamp: new Date()\n                    } : null\n            }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"]);\n            setOverallProgress(progress);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[updateProgress]\"], []);\n    // Simulate realistic progress over time\n    const simulateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": function(durationSeconds) {\n            let startProgress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n            }\n            const steps = Math.max(10, durationSeconds * 2); // Update every 500ms for smooth animation\n            const progressIncrement = (100 - startProgress) / steps;\n            let currentProgress = startProgress;\n            let stepCount = 0;\n            progressTimerRef.current = setInterval({\n                \"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\": ()=>{\n                    stepCount++;\n                    // Use easing function for more natural progress\n                    const easedProgress = startProgress + (100 - startProgress) * easeOutCubic(stepCount / steps);\n                    currentProgress = Math.min(easedProgress, 95); // Cap at 95% until manually completed\n                    updateProgress(currentProgress);\n                    if (stepCount >= steps) {\n                        if (progressTimerRef.current) {\n                            clearInterval(progressTimerRef.current);\n                            progressTimerRef.current = null;\n                        }\n                    }\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], 500);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[simulateProgress]\"], [\n        updateProgress\n    ]);\n    // Start orchestration tracking\n    const startTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[startTracking]\": ()=>{\n            setIsActive(true);\n            setOverallProgress(0);\n            setCurrentStep(null);\n            setAllSteps([]);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[startTracking]\"], []);\n    // Complete orchestration tracking\n    const completeTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setOverallProgress(100);\n            // Keep active for a bit to show completion state\n            setTimeout({\n                \"useMinimalistOrchestrationStatus.useCallback[completeTracking]\": ()=>{\n                    setIsActive(false);\n                }\n            }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], 3000);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[completeTracking]\"], []);\n    // Reset tracking\n    const resetTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMinimalistOrchestrationStatus.useCallback[resetTracking]\": ()=>{\n            if (progressTimerRef.current) {\n                clearInterval(progressTimerRef.current);\n                progressTimerRef.current = null;\n            }\n            setCurrentStep(null);\n            setAllSteps([]);\n            setIsActive(false);\n            setOverallProgress(0);\n        }\n    }[\"useMinimalistOrchestrationStatus.useCallback[resetTracking]\"], []);\n    return {\n        currentStep,\n        allSteps,\n        isActive,\n        overallProgress,\n        startStep,\n        updateStep,\n        completeStep,\n        errorStep,\n        updateProgress,\n        startTracking,\n        completeTracking,\n        resetTracking\n    };\n}\n// Easing function for smooth progress animation\nfunction easeOutCubic(t) {\n    return 1 - Math.pow(1 - t, 3);\n}\n// Predefined status steps for common orchestration phases\nconst MINIMALIST_STATUS_STEPS = {\n    // Connection and initialization\n    CONNECTING: {\n        type: 'connecting',\n        title: '🔗 Establishing Connections',\n        description: 'Setting up secure connections to AI providers...',\n        icon: 'connecting',\n        color: 'orange',\n        duration: 2\n    },\n    // Analysis phase\n    ANALYZING: {\n        type: 'analyzing',\n        title: '🔍 Analyzing Your Request',\n        description: 'RouKey AI is examining your request to understand the best approach...',\n        icon: 'analysis',\n        color: 'blue',\n        duration: 3,\n        details: [\n            'Understanding request complexity and requirements',\n            'Identifying the most suitable AI approach',\n            'Preparing optimal processing strategy'\n        ]\n    },\n    // Classification\n    CLASSIFICATION: {\n        type: 'classification',\n        title: '🧠 Understanding Your Needs',\n        description: 'Determining the perfect AI specialist for your request...',\n        icon: 'classification',\n        color: 'purple',\n        duration: 3,\n        details: [\n            'Analyzing request type and complexity',\n            'Matching requirements to specialist expertise',\n            'Selecting optimal AI configuration'\n        ]\n    },\n    // Agent preparation\n    PREPARING_SPECIALIST: {\n        type: 'agent_creation',\n        title: '🤖 Preparing AI Specialist',\n        description: 'Setting up the perfect expert for your task...',\n        icon: 'agents',\n        color: 'teal',\n        duration: 4,\n        details: [\n            'Configuring AI specialist with optimal parameters',\n            'Loading relevant knowledge and capabilities',\n            'Establishing secure processing environment'\n        ]\n    },\n    // Multi-agent coordination\n    COORDINATING_TEAM: {\n        type: 'supervisor_init',\n        title: '✨ Coordinating AI Team',\n        description: 'Setting up collaborative workflow between specialists...',\n        icon: 'supervisor',\n        color: 'orange',\n        duration: 2,\n        details: [\n            'Initializing team coordination protocols',\n            'Establishing communication channels',\n            'Preparing collaborative workspace'\n        ]\n    },\n    // Active processing\n    PROCESSING: {\n        type: 'agent_working',\n        title: '⚙️ AI Specialist Working',\n        description: 'Your dedicated expert is crafting the perfect response...',\n        icon: 'working',\n        color: 'cyan',\n        duration: 15,\n        details: [\n            'Applying specialized expertise to your request',\n            'Generating high-quality, tailored content',\n            'Ensuring accuracy and relevance'\n        ]\n    },\n    // Synthesis phase\n    SYNTHESIZING: {\n        type: 'supervisor_synthesis',\n        title: '🧪 Finalizing Response',\n        description: 'Combining all insights into your perfect response...',\n        icon: 'synthesis',\n        color: 'emerald',\n        duration: 5,\n        details: [\n            'Reviewing and combining all contributions',\n            'Ensuring coherence and quality',\n            'Applying final optimizations'\n        ]\n    },\n    // Simple thinking for single-agent\n    THINKING: {\n        type: 'thinking',\n        title: '🧠 AI Thinking',\n        description: 'Processing your request and formulating the best response...',\n        icon: 'thinking',\n        color: 'purple',\n        duration: 8,\n        details: [\n            'Analyzing your request in detail',\n            'Accessing relevant knowledge and context',\n            'Crafting personalized, high-quality response'\n        ]\n    },\n    // Generation phase\n    GENERATING: {\n        type: 'generating',\n        title: '✍️ Generating Response',\n        description: 'Creating your personalized response...',\n        icon: 'generating',\n        color: 'rose',\n        duration: 10,\n        details: [\n            'Applying AI expertise to your specific needs',\n            'Generating detailed, accurate content',\n            'Ensuring quality and relevance'\n        ]\n    },\n    // Optimization\n    OPTIMIZING: {\n        type: 'optimizing',\n        title: '⚡ Optimizing Results',\n        description: 'Fine-tuning for the best possible outcome...',\n        icon: 'optimizing',\n        color: 'green',\n        duration: 3,\n        details: [\n            'Reviewing generated content for quality',\n            'Applying final optimizations',\n            'Ensuring perfect alignment with your needs'\n        ]\n    },\n    // Validation\n    VALIDATING: {\n        type: 'validating',\n        title: '✅ Quality Check',\n        description: 'Ensuring accuracy and quality of the response...',\n        icon: 'validating',\n        color: 'indigo',\n        duration: 2,\n        details: [\n            'Performing comprehensive quality review',\n            'Validating accuracy and completeness',\n            'Confirming optimal response quality'\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMinimalistOrchestrationStatus.ts\n"));

/***/ })

});