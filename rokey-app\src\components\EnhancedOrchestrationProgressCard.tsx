'use client';

import React, { useState, useEffect } from 'react';
import {
  ChevronDownIcon,
  ChevronRightIcon,
  CheckCircleIcon,
  CogIcon,
  SparklesIcon,
  UserGroupIcon,
  BoltIcon,
  PencilIcon,
  BeakerIcon,
  RocketLaunchIcon,
  CommandLineIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  CpuChipIcon,
  LightBulbIcon,
  FireIcon,
  EyeIcon,
  BrainIcon,
  WrenchScrewdriverIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  CodeBracketIcon,
  PaintBrushIcon,
  AcademicCapIcon,
  StarIcon,
  ShieldCheckIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export interface EnhancedProgressStep {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  timestamp?: Date;
  details?: string[];
  metadata?: Record<string, any>;
  icon?: 'analysis' | 'roles' | 'workflow' | 'agents' | 'supervisor' | 'planning' | 'working' | 'synthesis' | 'connecting' | 'generating' | 'classification' | 'thinking' | 'building' | 'chatting' | 'writing' | 'coding' | 'designing' | 'learning' | 'optimizing' | 'validating';
  color?: 'blue' | 'purple' | 'green' | 'orange' | 'pink' | 'indigo' | 'teal' | 'red' | 'yellow' | 'emerald' | 'cyan' | 'rose';
  progress?: number; // 0-100 for progress bars
  duration?: number; // estimated duration in seconds
  agent?: string; // agent name for agent-specific steps
}

const STEP_ICONS = {
  analysis: MagnifyingGlassIcon,
  roles: UserGroupIcon,
  workflow: ArrowPathIcon,
  agents: CpuChipIcon,
  supervisor: SparklesIcon,
  planning: LightBulbIcon,
  working: CogIcon,
  synthesis: BeakerIcon,
  connecting: BoltIcon,
  generating: FireIcon,
  classification: EyeIcon,
  thinking: BrainIcon,
  building: WrenchScrewdriverIcon,
  chatting: ChatBubbleLeftRightIcon,
  writing: DocumentTextIcon,
  coding: CodeBracketIcon,
  designing: PaintBrushIcon,
  learning: AcademicCapIcon,
  optimizing: StarIcon,
  validating: ShieldCheckIcon
};

const COLOR_SCHEMES = {
  blue: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-blue-500/20 to-blue-600/30 border-blue-400/60 shadow-lg shadow-blue-500/20',
    completed: 'bg-gradient-to-br from-blue-600/30 to-blue-700/40 border-blue-400/70 shadow-lg shadow-blue-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-blue-100', completed: 'text-blue-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-blue-300', completed: 'text-blue-200', error: 'text-red-300' },
    accent: { pending: 'text-blue-400', in_progress: 'text-blue-200', completed: 'text-blue-100', error: 'text-red-200' }
  },
  purple: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-purple-500/20 to-purple-600/30 border-purple-400/60 shadow-lg shadow-purple-500/20',
    completed: 'bg-gradient-to-br from-purple-600/30 to-purple-700/40 border-purple-400/70 shadow-lg shadow-purple-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-purple-100', completed: 'text-purple-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-purple-300', completed: 'text-purple-200', error: 'text-red-300' },
    accent: { pending: 'text-purple-400', in_progress: 'text-purple-200', completed: 'text-purple-100', error: 'text-red-200' }
  },
  green: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-green-500/20 to-green-600/30 border-green-400/60 shadow-lg shadow-green-500/20',
    completed: 'bg-gradient-to-br from-green-600/30 to-green-700/40 border-green-400/70 shadow-lg shadow-green-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-green-100', completed: 'text-green-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-green-300', completed: 'text-green-200', error: 'text-red-300' },
    accent: { pending: 'text-green-400', in_progress: 'text-green-200', completed: 'text-green-100', error: 'text-red-200' }
  },
  orange: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-orange-500/20 to-orange-600/30 border-orange-400/60 shadow-lg shadow-orange-500/20',
    completed: 'bg-gradient-to-br from-orange-600/30 to-orange-700/40 border-orange-400/70 shadow-lg shadow-orange-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-orange-100', completed: 'text-orange-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-orange-300', completed: 'text-orange-200', error: 'text-red-300' },
    accent: { pending: 'text-orange-400', in_progress: 'text-orange-200', completed: 'text-orange-100', error: 'text-red-200' }
  },
  pink: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-pink-500/20 to-pink-600/30 border-pink-400/60 shadow-lg shadow-pink-500/20',
    completed: 'bg-gradient-to-br from-pink-600/30 to-pink-700/40 border-pink-400/70 shadow-lg shadow-pink-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-pink-100', completed: 'text-pink-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-pink-300', completed: 'text-pink-200', error: 'text-red-300' },
    accent: { pending: 'text-pink-400', in_progress: 'text-pink-200', completed: 'text-pink-100', error: 'text-red-200' }
  },
  indigo: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-indigo-500/20 to-indigo-600/30 border-indigo-400/60 shadow-lg shadow-indigo-500/20',
    completed: 'bg-gradient-to-br from-indigo-600/30 to-indigo-700/40 border-indigo-400/70 shadow-lg shadow-indigo-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-indigo-100', completed: 'text-indigo-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-indigo-300', completed: 'text-indigo-200', error: 'text-red-300' },
    accent: { pending: 'text-indigo-400', in_progress: 'text-indigo-200', completed: 'text-indigo-100', error: 'text-red-200' }
  },
  teal: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-teal-500/20 to-teal-600/30 border-teal-400/60 shadow-lg shadow-teal-500/20',
    completed: 'bg-gradient-to-br from-teal-600/30 to-teal-700/40 border-teal-400/70 shadow-lg shadow-teal-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-teal-100', completed: 'text-teal-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-teal-300', completed: 'text-teal-200', error: 'text-red-300' },
    accent: { pending: 'text-teal-400', in_progress: 'text-teal-200', completed: 'text-teal-100', error: 'text-red-200' }
  },
  cyan: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-cyan-500/20 to-cyan-600/30 border-cyan-400/60 shadow-lg shadow-cyan-500/20',
    completed: 'bg-gradient-to-br from-cyan-600/30 to-cyan-700/40 border-cyan-400/70 shadow-lg shadow-cyan-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-cyan-100', completed: 'text-cyan-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-cyan-300', completed: 'text-cyan-200', error: 'text-red-300' },
    accent: { pending: 'text-cyan-400', in_progress: 'text-cyan-200', completed: 'text-cyan-100', error: 'text-red-200' }
  },
  emerald: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-emerald-500/20 to-emerald-600/30 border-emerald-400/60 shadow-lg shadow-emerald-500/20',
    completed: 'bg-gradient-to-br from-emerald-600/30 to-emerald-700/40 border-emerald-400/70 shadow-lg shadow-emerald-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-emerald-100', completed: 'text-emerald-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-emerald-300', completed: 'text-emerald-200', error: 'text-red-300' },
    accent: { pending: 'text-emerald-400', in_progress: 'text-emerald-200', completed: 'text-emerald-100', error: 'text-red-200' }
  },
  rose: {
    pending: 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-600/50',
    in_progress: 'bg-gradient-to-br from-rose-500/20 to-rose-600/30 border-rose-400/60 shadow-lg shadow-rose-500/20',
    completed: 'bg-gradient-to-br from-rose-600/30 to-rose-700/40 border-rose-400/70 shadow-lg shadow-rose-400/25',
    error: 'bg-gradient-to-br from-red-500/20 to-red-600/30 border-red-400/60 shadow-lg shadow-red-500/20',
    text: { pending: 'text-slate-300', in_progress: 'text-rose-100', completed: 'text-rose-50', error: 'text-red-100' },
    icon: { pending: 'text-slate-400', in_progress: 'text-rose-300', completed: 'text-rose-200', error: 'text-red-300' },
    accent: { pending: 'text-rose-400', in_progress: 'text-rose-200', completed: 'text-rose-100', error: 'text-red-200' }
  }
};

interface EnhancedOrchestrationProgressCardProps {
  step: EnhancedProgressStep;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  className?: string;
  showProgress?: boolean;
  animationDelay?: number;
}

export default function EnhancedOrchestrationProgressCard({
  step,
  isExpanded = false,
  onToggleExpand,
  className = '',
  showProgress = true,
  animationDelay = 0
}: EnhancedOrchestrationProgressCardProps) {
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentProgress, setCurrentProgress] = useState(0);
  
  const colorScheme = COLOR_SCHEMES[step.color || 'blue'];
  const IconComponent = step.icon ? STEP_ICONS[step.icon] : CogIcon;
  const ChevronIcon = isExpanded ? ChevronDownIcon : ChevronRightIcon;

  // Animate status changes
  useEffect(() => {
    if (step.status === 'completed' || step.status === 'in_progress') {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 800);
      return () => clearTimeout(timer);
    }
  }, [step.status]);

  // Animate progress bar
  useEffect(() => {
    if (step.status === 'in_progress' && step.progress !== undefined) {
      const timer = setTimeout(() => {
        setCurrentProgress(step.progress || 0);
      }, 100);
      return () => clearTimeout(timer);
    } else if (step.status === 'completed') {
      setCurrentProgress(100);
    }
  }, [step.progress, step.status]);

  const handleToggle = () => {
    if (onToggleExpand && (step.details?.length || step.description)) {
      onToggleExpand();
    }
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div 
      className={`transition-all duration-500 ${className}`}
      style={{ animationDelay: `${animationDelay}ms` }}
    >
      <div 
        className={`
          relative rounded-2xl border backdrop-blur-sm transition-all duration-500
          ${colorScheme[step.status]}
          ${isAnimating ? 'scale-[1.02] shadow-2xl' : 'scale-100'}
          hover:scale-[1.01] hover:shadow-xl
          transform-gpu
        `}
      >
        {/* Animated background glow for in-progress items */}
        {step.status === 'in_progress' && (
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse" />
        )}

        {/* Main card content */}
        <div 
          className={`
            flex items-center p-5 cursor-pointer transition-all duration-300
            ${(step.details?.length || step.description) ? 'hover:bg-white/5' : ''}
          `}
          onClick={handleToggle}
        >
          {/* Status icon with enhanced animations */}
          <div className="flex-shrink-0 mr-4">
            {step.status === 'completed' ? (
              <div className="relative">
                <CheckCircleIcon className={`w-7 h-7 ${colorScheme.icon[step.status]} transition-all duration-500`} />
                <div className="absolute inset-0 rounded-full bg-current opacity-20 animate-ping" />
              </div>
            ) : step.status === 'in_progress' ? (
              <div className="relative">
                <IconComponent className={`w-7 h-7 ${colorScheme.icon[step.status]} transition-all duration-500`} />
                {/* Multi-layer spinner */}
                <div className="absolute inset-0 border-2 border-transparent border-t-current rounded-full animate-spin opacity-60" />
                <div className="absolute inset-1 border border-transparent border-t-current rounded-full animate-spin opacity-40" 
                     style={{ animationDirection: 'reverse', animationDuration: '2s' }} />
                <div className="absolute inset-2 border border-transparent border-t-current rounded-full animate-spin opacity-20" 
                     style={{ animationDuration: '3s' }} />
              </div>
            ) : step.status === 'error' ? (
              <div className={`w-7 h-7 rounded-full border-2 ${colorScheme[step.status]} flex items-center justify-center`}>
                <div className="w-3 h-3 rounded-full bg-red-400 animate-pulse" />
              </div>
            ) : (
              <div className={`w-7 h-7 rounded-full border-2 ${colorScheme[step.status]} flex items-center justify-center`}>
                <IconComponent className={`w-4 h-4 ${colorScheme.icon[step.status]}`} />
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h3 className={`text-base font-semibold ${colorScheme.text[step.status]} transition-colors duration-500`}>
                {step.title}
                {step.agent && (
                  <span className={`ml-2 text-sm font-normal ${colorScheme.accent[step.status]}`}>
                    • {step.agent}
                  </span>
                )}
              </h3>
              
              {/* Timestamp and duration */}
              <div className="flex items-center space-x-3 text-xs">
                {step.duration && step.status === 'in_progress' && (
                  <div className={`flex items-center space-x-1 ${colorScheme.accent[step.status]}`}>
                    <ClockIcon className="w-3 h-3" />
                    <span>~{formatDuration(step.duration)}</span>
                  </div>
                )}
                {step.timestamp && (
                  <span className="text-gray-500">
                    {step.timestamp.toLocaleTimeString()}
                  </span>
                )}
              </div>
            </div>
            
            {/* Description */}
            {step.description && (
              <p className={`text-sm ${colorScheme.text[step.status]} opacity-90 leading-relaxed mb-2`}>
                {step.description}
              </p>
            )}

            {/* Progress bar */}
            {showProgress && step.status === 'in_progress' && (
              <div className="mt-3">
                <div className="flex justify-between items-center mb-1">
                  <span className={`text-xs ${colorScheme.accent[step.status]}`}>
                    Progress
                  </span>
                  <span className={`text-xs ${colorScheme.accent[step.status]}`}>
                    {Math.round(currentProgress)}%
                  </span>
                </div>
                <div className="h-2 bg-black/20 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-current to-current opacity-80 transition-all duration-1000 ease-out relative overflow-hidden"
                    style={{ width: `${currentProgress}%` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Expand chevron */}
          {(step.details?.length || step.description) && (
            <div className="flex-shrink-0 ml-3">
              <ChevronIcon className={`w-5 h-5 ${colorScheme.icon[step.status]} transition-transform duration-300`} />
            </div>
          )}
        </div>

        {/* Expanded details */}
        {isExpanded && step.details && step.details.length > 0 && (
          <div className="border-t border-white/10 px-5 py-4 space-y-2">
            {step.details.map((detail, index) => (
              <div key={index} className={`text-sm ${colorScheme.text[step.status]} opacity-80 flex items-start space-x-2`}>
                <div className={`w-1.5 h-1.5 rounded-full ${colorScheme.accent[step.status]} mt-2 flex-shrink-0`} />
                <span>{detail}</span>
              </div>
            ))}
          </div>
        )}

        {/* Completion celebration effect */}
        {step.status === 'completed' && isAnimating && (
          <div className="absolute inset-0 rounded-2xl overflow-hidden pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
          </div>
        )}
      </div>
    </div>
  );
}
